Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to board.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_callback.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for SYSCFG_DL_PWM_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for SYSCFG_DL_ADC12_VOLTAGE_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for DL_ADC12_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for DL_ADC12_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for DL_GPIO_setUpperPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.rodata.gPWM_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for DL_Timer_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for DL_UART_enableFIFOs
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for DL_UART_setRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for DL_UART_setTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for DL_UART_enableLoopbackMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for DL_UART_enableDMAReceiveEvent
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for DL_ADC12_initSingleSample
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for DL_ADC12_configConversionMem
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for DL_ADC12_setSampleTime0
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for DL_ADC12_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for DL_ADC12_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for DL_SYSTICK_enable
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_0Backup) for gPWM_0Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs) refers to ti_msp_dl_config.o(.text.DL_UART_enableFIFOs) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode) refers to ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent) refers to ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable) refers to ti_msp_dl_config.o(.text.DL_SYSTICK_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    empty.o(.text.main) refers to empty.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    empty.o(.text.main) refers to initial.o(.text.Initial) for Initial
    empty.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to empty.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to empty.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    board.o(.text.SysTick_Handler) refers to board.o(.bss.g_msTicks) for g_msTicks
    board.o(.ARM.exidx.text.SysTick_Handler) refers to board.o(.text.SysTick_Handler) for [Anonymous Symbol]
    board.o(.text.TI_GetTick) refers to board.o(.bss.g_msTicks) for g_msTicks
    board.o(.ARM.exidx.text.TI_GetTick) refers to board.o(.text.TI_GetTick) for [Anonymous Symbol]
    board.o(.text.fputc) refers to board.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    board.o(.text.fputc) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputc) refers to board.o(.text.fputc) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.DL_UART_isBusy) refers to board.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    board.o(.text.fputs) refers to strlen.o(.text) for strlen
    board.o(.text.fputs) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    board.o(.ARM.exidx.text.fputs) refers to board.o(.text.fputs) for [Anonymous Symbol]
    board.o(.text.puts) refers to board.o(.text.fputs) for fputs
    board.o(.text.puts) refers to stdout.o(.data) for __stdout
    board.o(.text.puts) refers to board.o(.rodata.str1.1) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.puts) refers to board.o(.text.puts) for [Anonymous Symbol]
    board.o(.ARM.exidx.text.Systick_getTick) refers to board.o(.text.Systick_getTick) for [Anonymous Symbol]
    board.o(.text.delay_ms) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_ms) refers to board.o(.text.delay_ms) for [Anonymous Symbol]
    board.o(.text.delay_us) refers to board.o(.text.Systick_getTick) for Systick_getTick
    board.o(.ARM.exidx.text.delay_us) refers to board.o(.text.delay_us) for [Anonymous Symbol]
    board.o(.text.delay_1us) refers to board.o(.text.delay_us) for delay_us
    board.o(.ARM.exidx.text.delay_1us) refers to board.o(.text.delay_1us) for [Anonymous Symbol]
    board.o(.text.delay_1ms) refers to board.o(.text.delay_ms) for delay_ms
    board.o(.ARM.exidx.text.delay_1ms) refers to board.o(.text.delay_1ms) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_disable) for DL_UART_disable
    dl_uart.o(.text.DL_UART_init) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_disable) refers to dl_uart.o(.text.DL_UART_disable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_uart.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling) refers to dl_uart.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to dl_uart.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for DL_UART_setIrDAPulseLength
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveData) refers to dl_uart.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitData) refers to dl_uart.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_isRXFIFOEmpty) for DL_UART_isRXFIFOEmpty
    dl_uart.o(.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_isTXFIFOFull) for DL_UART_isTXFIFOFull
    dl_uart.o(.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_enable) refers to dl_uart.o(.text.DL_UART_enable) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_enable) for DL_UART_enable
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for DL_TRNG_sendCommand
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_trng.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_trng.o(.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand) refers to dl_trng.o(.text.DL_TRNG_sendCommand) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    dl_timer.o(.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_timer.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for DL_Timer_getInChanPairConfig
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig) refers to dl_timer.o(.text.DL_Timer_getInChanPairConfig) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_getInChanConfig) for DL_Timer_getInChanConfig
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setLoadValue) for DL_Timer_setLoadValue
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable) for DL_Timer_setCounterValueAfterEnable
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for DL_Timer_setCaptureCompareAction
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for DL_Timer_setCaptureCompareCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for DL_Timer_setCaptureCompareInput
    dl_timer.o(.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_init) refers to dl_spi.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_spi.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_receiveData8) for DL_SPI_receiveData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_receiveData16) for DL_SPI_receiveData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_isRXFIFOEmpty) for DL_SPI_isRXFIFOEmpty
    dl_spi.o(.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_receiveData32) for DL_SPI_receiveData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_transmitData8) for DL_SPI_transmitData8
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_transmitData16) for DL_SPI_transmitData16
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_enable) for DL_SPI_enable
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_enable) refers to dl_spi.o(.text.DL_SPI_enable) for [Anonymous Symbol]
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_isTXFIFOFull) for DL_SPI_isTXFIFOFull
    dl_spi.o(.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_transmitData32) for DL_SPI_transmitData32
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for DL_RTC_Common_setClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for DL_RTC_Common_setCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for DL_RTC_Common_setCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for DL_RTC_Common_setCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for DL_RTC_Common_setCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for DL_RTC_Common_setCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for DL_RTC_Common_setCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for DL_RTC_Common_setCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for DL_RTC_Common_setCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for DL_RTC_Common_setCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for DL_RTC_Common_setCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for DL_RTC_Common_getCalendarSecondsBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for DL_RTC_Common_getCalendarMinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for DL_RTC_Common_getCalendarHoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for DL_RTC_Common_getCalendarDayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for DL_RTC_Common_getCalendarDayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for DL_RTC_Common_getCalendarMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for DL_RTC_Common_getCalendarYearBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for DL_RTC_Common_getCalendarSecondsBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for DL_RTC_Common_getCalendarMinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for DL_RTC_Common_getCalendarHoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for DL_RTC_Common_getCalendarDayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for DL_RTC_Common_getCalendarDayOfMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for DL_RTC_Common_getCalendarMonthBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for DL_RTC_Common_getCalendarYearBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for DL_RTC_Common_setAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for DL_RTC_Common_setAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for DL_RTC_Common_setAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for DL_RTC_Common_setAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for DL_RTC_Common_setAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for DL_RTC_Common_setAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for DL_RTC_Common_setAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for DL_RTC_Common_setAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for DL_RTC_Common_getAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for DL_RTC_Common_getAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for DL_RTC_Common_getAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for DL_RTC_Common_getAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for DL_RTC_Common_getAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for DL_RTC_Common_getAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for DL_RTC_Common_getAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for DL_RTC_Common_getAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for DL_RTC_Common_enableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for DL_RTC_Common_enableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for DL_RTC_Common_enableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for DL_RTC_Common_enableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for DL_RTC_Common_enableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for DL_RTC_Common_enableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for DL_RTC_Common_enableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for DL_RTC_Common_enableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for DL_RTC_Common_disableAlarm1MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for DL_RTC_Common_disableAlarm1HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for DL_RTC_Common_disableAlarm1DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for DL_RTC_Common_disableAlarm1DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for DL_RTC_Common_disableAlarm1MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for DL_RTC_Common_disableAlarm1HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for DL_RTC_Common_disableAlarm1DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for DL_RTC_Common_disableAlarm1DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for DL_RTC_Common_setAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for DL_RTC_Common_setAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for DL_RTC_Common_setAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for DL_RTC_Common_setAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for DL_RTC_Common_setAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for DL_RTC_Common_setAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for DL_RTC_Common_setAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for DL_RTC_Common_setAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for DL_RTC_Common_getAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for DL_RTC_Common_getAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for DL_RTC_Common_getAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for DL_RTC_Common_getAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for DL_RTC_Common_getAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for DL_RTC_Common_getAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for DL_RTC_Common_getAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for DL_RTC_Common_getAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for DL_RTC_Common_enableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for DL_RTC_Common_enableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for DL_RTC_Common_enableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for DL_RTC_Common_enableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for DL_RTC_Common_enableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for DL_RTC_Common_enableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for DL_RTC_Common_enableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for DL_RTC_Common_enableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat) for DL_RTC_Common_getClockFormat
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for DL_RTC_Common_disableAlarm2MinutesBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for DL_RTC_Common_disableAlarm2HoursBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for DL_RTC_Common_disableAlarm2DayOfWeekBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for DL_RTC_Common_disableAlarm2DayOfMonthBinary
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for DL_RTC_Common_disableAlarm2MinutesBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for DL_RTC_Common_disableAlarm2HoursBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for DL_RTC_Common_disableAlarm2DayOfWeekBCD
    dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for DL_RTC_Common_disableAlarm2DayOfMonthBCD
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_getGain) refers to dl_opa.o(.text.DL_OPA_getGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_opa.o(.ARM.exidx.text.DL_OPA_setGain) refers to dl_opa.o(.text.DL_OPA_setGain) for [Anonymous Symbol]
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_getGain) for DL_OPA_getGain
    dl_opa.o(.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_setGain) for DL_OPA_setGain
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_opa.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_mcan.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for DL_MCAN_writeMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg) refers to dl_mcan.o(.text.DL_MCAN_writeMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_getMsgObjSize) for DL_MCAN_getMsgObjSize
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for DL_MCAN_readMsg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for DL_MCAN_getDataSize
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg) refers to dl_mcan.o(.text.DL_MCAN_readMsg) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.HW_WR_REG32_RAW) for HW_WR_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock) for DL_MCAN_writeProtectedRegAccessUnlock
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock) for DL_MCAN_writeProtectedRegAccessLock
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.HW_WR_FIELD32_RAW) for HW_WR_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccLoadRegister) for DL_MCAN_eccLoadRegister
    dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.HW_RD_REG32_RAW) for HW_RD_REG32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.HW_RD_FIELD32_RAW) for HW_RD_FIELD32_RAW
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize) refers to dl_mcan.o(.text.DL_MCAN_getDataSize) for [Anonymous Symbol]
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for DL_MathACL_setOperandTwo
    dl_mathacl.o(.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for DL_MathACL_setOperandOne
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo) refers to dl_mathacl.o(.text.DL_MathACL_setOperandTwo) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne) refers to dl_mathacl.o(.text.DL_MathACL_setOperandOne) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_i2c.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for DL_I2C_isControllerTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for DL_I2C_transmitControllerData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData) refers to dl_i2c.o(.text.DL_I2C_transmitControllerData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for DL_I2C_startFlushControllerTXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for DL_I2C_isControllerTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for DL_I2C_stopFlushControllerTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for DL_I2C_startFlushControllerRXFIFO
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for DL_I2C_isControllerRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for DL_I2C_stopFlushControllerRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for DL_I2C_startFlushTargetTXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for DL_I2C_isTargetTXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for DL_I2C_stopFlushTargetTXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for DL_I2C_startFlushTargetRXFIFO
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for DL_I2C_stopFlushTargetRXFIFO
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull) for DL_I2C_isTargetTXFIFOFull
    dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetData) for DL_I2C_transmitTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_getTargetStatus) for DL_I2C_getTargetStatus
    dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for [Anonymous Symbol]
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty) for DL_I2C_isTargetRXFIFOEmpty
    dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetData) for DL_I2C_receiveTargetData
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for DL_FlashCTL_unprotectDataMemory
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for DL_SYSCTL_isFlashBankSwapEnabled
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for DL_FlashCTL_enableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for DL_FlashCTL_setBankSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for DL_FlashCTL_setRegionSelect
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for DL_FlashCTL_unprotectMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for DL_FlashCTL_protectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for DL_FlashCTL_disableAddressOverrideMode
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize) for DL_FactoryRegion_getDATAFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for DL_CORE_getInstructionConfig
    dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for DL_CORE_configInstruction
    dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled) refers to dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setBankSelect) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect) refers to dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode) refers to dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for DL_FlashCTL_massErase
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for DL_FlashCTL_massEraseFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for DL_FlashCTL_eraseMemoryFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for DL_FlashCTL_unprotectNonMainMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for DL_FlashCTL_eraseMemory
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for DL_FlashCTL_programMemoryConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config) for DL_FlashCTL_programMemory8Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config) for DL_FlashCTL_programMemory16Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config) for DL_FlashCTL_programMemory32Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config) for DL_FlashCTL_programMemory64Config
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for DL_FlashCTL_programMemory64WithECCGenerated
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for DL_FlashCTL_programMemoryFromRAM64WithECCGenerated
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for DL_FlashCTL_programMemory64WithECCManual
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for DL_FlashCTL_programMemoryFromRAM64WithECCManual
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for DL_FlashCTL_programMemory32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for DL_FlashCTL_programMemory64
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone) for DL_FlashCTL_waitForCmdDone
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for DL_FlashCTL_programMemoryFromRAM32
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for DL_FlashCTL_programMemoryFromRAM64
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber) for DL_FlashCTL_getFlashSectorNumber
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank) for DL_FlashCTL_getFlashSectorNumberInBank
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks) for DL_FactoryRegion_getNumBanks
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize) for DL_FactoryRegion_getMAINFlashSize
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank) for DL_SYSCTL_isExecuteFromUpperFlashBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for DL_FlashCTL_readVerifyConfig
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config) for DL_FlashCTL_readVerify8Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config) for DL_FlashCTL_readVerify16Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config) for DL_FlashCTL_readVerify32Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config) for DL_FlashCTL_readVerify64Config
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig) refers to dl_flashctl.o(.text.DL_CORE_getInstructionConfig) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction) refers to dl_flashctl.o(.text.DL_CORE_configInstruction) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_flashctl.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress) for DL_FlashCTL_setCommandAddress
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_configTransfer) for DL_DMA_configTransfer
    dl_dma.o(.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_setTrigger) for DL_DMA_setTrigger
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer) refers to dl_dma.o(.text.DL_DMA_configTransfer) for [Anonymous Symbol]
    dl_dma.o(.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger) refers to dl_dma.o(.text.DL_DMA_setTrigger) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dma.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for DL_DAC12_configDataFormat
    dl_dac12.o(.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat) refers to dl_dac12.o(.text.DL_DAC12_configDataFormat) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_dac12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for DL_DAC12_getInterruptStatus
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8) refers to dl_dac12.o(.text.DL_DAC12_output8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12) refers to dl_dac12.o(.text.DL_DAC12_output12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_output8) for DL_DAC12_output8
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_isFIFOFull) for DL_DAC12_isFIFOFull
    dl_dac12.o(.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_output12) for DL_DAC12_output12
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for DL_DAC12_startCalibration
    dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for DL_DAC12_isCalibrationRunning
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration) refers to dl_dac12.o(.text.DL_DAC12_startCalibration) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning) refers to dl_dac12.o(.text.DL_DAC12_isCalibrationRunning) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus) refers to dl_dac12.o(.text.DL_DAC12_getInterruptStatus) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32) refers to dl_crc.o(.text.DL_CRC_feedData32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32) refers to dl_crc.o(.text.DL_CRC_getResult32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_setSeed32) for DL_CRC_setSeed32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_feedData32) for DL_CRC_feedData32
    dl_crc.o(.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_getResult32) for DL_CRC_getResult32
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16) refers to dl_crc.o(.text.DL_CRC_feedData16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16) refers to dl_crc.o(.text.DL_CRC_getResult16) for [Anonymous Symbol]
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_setSeed16) for DL_CRC_setSeed16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_feedData16) for DL_CRC_feedData16
    dl_crc.o(.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_getResult16) for DL_CRC_getResult16
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for DL_AES_setKeyAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord) refers to dl_aes.o(.text.DL_AES_loadDataWord) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for DL_AES_loadDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for DL_AES_getDataOutAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for DL_AES_loadXORDataInAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer) for DL_AES_checkAlignmentAndReturnPointer
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for DL_AES_loadXORDataInWithoutTriggerAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadDataWord) for DL_AES_loadDataWord
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for DL_AES_xorDataAligned
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_adc12.o(.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg) refers to dl_adc12.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Refresh_Gram) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Refresh_Gram) refers to oled.o(.text.OLED_Refresh_Gram) for [Anonymous Symbol]
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Set) for OLED_RS_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_RS_Clr) for OLED_RS_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Clr) for OLED_SCLK_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Set) for OLED_SDIN_Set
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SDIN_Clr) for OLED_SDIN_Clr
    oled.o(.text.OLED_WR_Byte) refers to oled.o(.text.OLED_SCLK_Set) for OLED_SCLK_Set
    oled.o(.ARM.exidx.text.OLED_WR_Byte) refers to oled.o(.text.OLED_WR_Byte) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RS_Set) refers to oled.o(.text.OLED_RS_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_RS_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RS_Clr) refers to oled.o(.text.OLED_RS_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Clr) refers to oled.o(.text.OLED_SCLK_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Set) refers to oled.o(.text.OLED_SDIN_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_SDIN_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_SDIN_Clr) refers to oled.o(.text.OLED_SDIN_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_SCLK_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_SCLK_Set) refers to oled.o(.text.OLED_SCLK_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_GRAM) for OLED_GRAM
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1608) for oled_asc2_1608
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.oled_asc2_1206) for oled_asc2_1206
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.oled_pow) refers to oled.o(.text.oled_pow) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.oled_pow) for oled_pow
    oled.o(.text.OLED_ShowNumber) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    oled.o(.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNumber) refers to oled.o(.text.OLED_ShowNumber) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Clr) for OLED_RST_Clr
    oled.o(.text.OLED_Init) refers to board.o(.text.delay_ms) for delay_ms
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_RST_Set) for OLED_RST_Set
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Clr) refers to oled.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    oled.o(.ARM.exidx.text.OLED_RST_Clr) refers to oled.o(.text.OLED_RST_Clr) for [Anonymous Symbol]
    oled.o(.text.OLED_RST_Set) refers to oled.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    oled.o(.ARM.exidx.text.OLED_RST_Set) refers to oled.o(.text.OLED_RST_Set) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to oled.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.DL_GPIO_setPins) refers to oled.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    key.o(.text.keyValue) refers to key.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    key.o(.ARM.exidx.text.keyValue) refers to key.o(.text.keyValue) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.DL_GPIO_readPins) refers to key.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to ffltui.o(.text) for __aeabi_ui2f
    key.o(.text.key_scan) refers to fdiv.o(.text) for __aeabi_fdiv
    key.o(.text.key_scan) refers to fmul.o(.text) for __aeabi_fmul
    key.o(.text.key_scan) refers to key.o(.text.keyValue) for keyValue
    key.o(.text.key_scan) refers to ffixi.o(.text) for __aeabi_f2iz
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.check_once) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.press_flag) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.time_core) for [Anonymous Symbol]
    key.o(.text.key_scan) refers to key.o(.bss.key_scan.long_press_time) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.key_scan) refers to key.o(.text.key_scan) for [Anonymous Symbol]
    led.o(.text.LED_ON) refers to led.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    led.o(.ARM.exidx.text.LED_ON) refers to led.o(.text.LED_ON) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to led.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    led.o(.text.LED_OFF) refers to led.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    led.o(.ARM.exidx.text.LED_OFF) refers to led.o(.text.LED_OFF) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_setPins) refers to led.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    led.o(.text.LED_Toggle) refers to led.o(.text.DL_GPIO_togglePins) for DL_GPIO_togglePins
    led.o(.ARM.exidx.text.LED_Toggle) refers to led.o(.text.LED_Toggle) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.DL_GPIO_togglePins) refers to led.o(.text.DL_GPIO_togglePins) for [Anonymous Symbol]
    led.o(.text.LED_Flash) refers to led.o(.text.LED_ON) for LED_ON
    led.o(.text.LED_Flash) refers to led.o(.text.LED_Toggle) for LED_Toggle
    led.o(.text.LED_Flash) refers to led.o(.bss.LED_Flash.temp) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_Flash) refers to led.o(.text.LED_Flash) for [Anonymous Symbol]
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor.o(.text.Set_PWM) refers to motor.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor.o(.text.Set_PWM) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Set_PWM) refers to motor.o(.text.Set_PWM) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_setPins) refers to motor.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    motor.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to motor.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup1) for gpio_interrup1
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.gpio_interrup2) for gpio_interrup2
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    encoder.o(.text.GROUP1_IRQHandler) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    datascope_dp.o(.ARM.exidx.text.Float2Byte) refers to datascope_dp.o(.text.Float2Byte) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.Float2Byte) for Float2Byte
    datascope_dp.o(.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data) refers to datascope_dp.o(.text.DataScope_Get_Channel_Data) for [Anonymous Symbol]
    datascope_dp.o(.text.DataScope_Data_Generate) refers to datascope_dp.o(.bss.DataScope_OutPut_Buffer) for DataScope_OutPut_Buffer
    datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate) refers to datascope_dp.o(.text.DataScope_Data_Generate) for [Anonymous Symbol]
    mpu6050.o(.ARM.exidx.text.Delay_Us) refers to mpu6050.o(.text.Delay_Us) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_WriteReg) refers to myi2c.o(.text.MyI2C_Start) for MyI2C_Start
    mpu6050.o(.text.MPU6050_WriteReg) refers to myi2c.o(.text.MyI2C_SendByte) for MyI2C_SendByte
    mpu6050.o(.text.MPU6050_WriteReg) refers to myi2c.o(.text.MyI2C_ReceiveAck) for MyI2C_ReceiveAck
    mpu6050.o(.text.MPU6050_WriteReg) refers to myi2c.o(.text.MyI2C_Stop) for MyI2C_Stop
    mpu6050.o(.text.MPU6050_WriteReg) refers to mpu6050.o(.text.Delay_Us) for Delay_Us
    mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg) refers to mpu6050.o(.text.MPU6050_WriteReg) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_Start) for MyI2C_Start
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_SendByte) for MyI2C_SendByte
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_ReceiveAck) for MyI2C_ReceiveAck
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_ReceiveByte) for MyI2C_ReceiveByte
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_SendAck) for MyI2C_SendAck
    mpu6050.o(.text.MPU6050_ReadReg) refers to myi2c.o(.text.MyI2C_Stop) for MyI2C_Stop
    mpu6050.o(.ARM.exidx.text.MPU6050_ReadReg) refers to mpu6050.o(.text.MPU6050_ReadReg) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.text.MPU6050_GetData) for MPU6050_GetData
    mpu6050.o(.text.MPU6050_Calibrate) refers to idiv_div0.o(.text) for __aeabi_idiv
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.AccX_offset) for AccX_offset
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.AccY_offset) for AccY_offset
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.AccZ_offset) for AccZ_offset
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.GyroX_offset) for GyroX_offset
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.GyroY_offset) for GyroY_offset
    mpu6050.o(.text.MPU6050_Calibrate) refers to mpu6050.o(.bss.GyroZ_offset) for GyroZ_offset
    mpu6050.o(.ARM.exidx.text.MPU6050_Calibrate) refers to mpu6050.o(.text.MPU6050_Calibrate) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_GetData) refers to mpu6050.o(.text.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050.o(.ARM.exidx.text.MPU6050_GetData) refers to mpu6050.o(.text.MPU6050_GetData) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_Init) refers to mpu6050.o(.text.MPU6050_WriteReg) for MPU6050_WriteReg
    mpu6050.o(.text.MPU6050_Init) refers to mpu6050.o(.text.MPU6050_Calibrate) for MPU6050_Calibrate
    mpu6050.o(.ARM.exidx.text.MPU6050_Init) refers to mpu6050.o(.text.MPU6050_Init) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_GetID) refers to mpu6050.o(.text.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050.o(.ARM.exidx.text.MPU6050_GetID) refers to mpu6050.o(.text.MPU6050_GetID) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_GetRealData) refers to mpu6050.o(.text.MPU6050_ReadReg) for MPU6050_ReadReg
    mpu6050.o(.text.MPU6050_GetRealData) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.MPU6050_GetRealData) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.ARM.exidx.text.MPU6050_GetRealData) refers to mpu6050.o(.text.MPU6050_GetRealData) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.text.MPU6050_GetData) for MPU6050_GetData
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.AccX_offset) for AccX_offset
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.AccY_offset) for AccY_offset
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.AccZ_offset) for AccZ_offset
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.GyroX_offset) for GyroX_offset
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.GyroY_offset) for GyroY_offset
    mpu6050.o(.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.bss.GyroZ_offset) for GyroZ_offset
    mpu6050.o(.ARM.exidx.text.MPU6050_GetCalibratedData) refers to mpu6050.o(.text.MPU6050_GetCalibratedData) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to board.o(.text.TI_GetTick) for TI_GetTick
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to ffltui.o(.text) for __aeabi_ui2f
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to fdiv.o(.text) for __aeabi_fdiv
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to fmul.o(.text) for __aeabi_fmul
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to fadd.o(.text) for __aeabi_fadd
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to mpu6050.o(.bss.lastUpdateTime) for lastUpdateTime
    mpu6050.o(.text.MPU6050_CalculateZAngle) refers to mpu6050.o(.bss.Z_Angle) for Z_Angle
    mpu6050.o(.ARM.exidx.text.MPU6050_CalculateZAngle) refers to mpu6050.o(.text.MPU6050_CalculateZAngle) for [Anonymous Symbol]
    mpu6050.o(.text.MPU6050_ZiTaiJieSuan) refers to mpu6050.o(.text.MPU6050_GetCalibratedData) for MPU6050_GetCalibratedData
    mpu6050.o(.text.MPU6050_ZiTaiJieSuan) refers to fflti.o(.text) for __aeabi_i2f
    mpu6050.o(.text.MPU6050_ZiTaiJieSuan) refers to mpu6050.o(.text.MPU6050_CalculateZAngle) for MPU6050_CalculateZAngle
    mpu6050.o(.ARM.exidx.text.MPU6050_ZiTaiJieSuan) refers to mpu6050.o(.text.MPU6050_ZiTaiJieSuan) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_W_SCL) refers to myi2c.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    myi2c.o(.text.MyI2C_W_SCL) refers to myi2c.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    myi2c.o(.ARM.exidx.text.MyI2C_W_SCL) refers to myi2c.o(.text.MyI2C_W_SCL) for [Anonymous Symbol]
    myi2c.o(.ARM.exidx.text.DL_GPIO_setPins) refers to myi2c.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    myi2c.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to myi2c.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_W_SDA) refers to myi2c.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    myi2c.o(.text.MyI2C_W_SDA) refers to myi2c.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    myi2c.o(.ARM.exidx.text.MyI2C_W_SDA) refers to myi2c.o(.text.MyI2C_W_SDA) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_R_SDA) refers to myi2c.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    myi2c.o(.ARM.exidx.text.MyI2C_R_SDA) refers to myi2c.o(.text.MyI2C_R_SDA) for [Anonymous Symbol]
    myi2c.o(.ARM.exidx.text.DL_GPIO_readPins) refers to myi2c.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_Init) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.text.MyI2C_Init) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.ARM.exidx.text.MyI2C_Init) refers to myi2c.o(.text.MyI2C_Init) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_Start) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_Start) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.ARM.exidx.text.MyI2C_Start) refers to myi2c.o(.text.MyI2C_Start) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_Stop) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_Stop) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.ARM.exidx.text.MyI2C_Stop) refers to myi2c.o(.text.MyI2C_Stop) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_SendByte) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_SendByte) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.ARM.exidx.text.MyI2C_SendByte) refers to myi2c.o(.text.MyI2C_SendByte) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_ReceiveByte) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_ReceiveByte) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.text.MyI2C_ReceiveByte) refers to myi2c.o(.text.MyI2C_R_SDA) for MyI2C_R_SDA
    myi2c.o(.ARM.exidx.text.MyI2C_ReceiveByte) refers to myi2c.o(.text.MyI2C_ReceiveByte) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_SendAck) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_SendAck) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.ARM.exidx.text.MyI2C_SendAck) refers to myi2c.o(.text.MyI2C_SendAck) for [Anonymous Symbol]
    myi2c.o(.text.MyI2C_ReceiveAck) refers to myi2c.o(.text.MyI2C_W_SDA) for MyI2C_W_SDA
    myi2c.o(.text.MyI2C_ReceiveAck) refers to myi2c.o(.text.MyI2C_W_SCL) for MyI2C_W_SCL
    myi2c.o(.text.MyI2C_ReceiveAck) refers to myi2c.o(.text.MyI2C_R_SDA) for MyI2C_R_SDA
    myi2c.o(.ARM.exidx.text.MyI2C_ReceiveAck) refers to myi2c.o(.text.MyI2C_ReceiveAck) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_enable_task) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_enable_task) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_enable_task) refers to scheduler.o(.text.scheduler_enable_task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_disable_task) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_disable_task) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_disable_task) refers to scheduler.o(.text.scheduler_disable_task) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to board.o(.text.TI_GetTick) for TI_GetTick
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_callback.o(.text.BTBufferHandler) for BTBufferHandler
    scheduler.o(.data.scheduler_task) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    scheduler.o(.data.scheduler_task) refers to track.o(.text.Track_Judge) for Track_Judge
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_disableChannel) for DL_DMA_disableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for DL_DMA_setSrcAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for DL_DMA_setDestAddr
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for DL_DMA_setTransferSize
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.text.DL_DMA_enableChannel) for DL_DMA_enableChannel
    uart_callback.o(.text.BT_DAMConfig) refers to uart_callback.o(.bss.UART1Packet) for UART1Packet
    uart_callback.o(.ARM.exidx.text.BT_DAMConfig) refers to uart_callback.o(.text.BT_DAMConfig) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel) refers to uart_callback.o(.text.DL_DMA_disableChannel) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr) refers to uart_callback.o(.text.DL_DMA_setSrcAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr) refers to uart_callback.o(.text.DL_DMA_setDestAddr) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize) refers to uart_callback.o(.text.DL_DMA_setTransferSize) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel) refers to uart_callback.o(.text.DL_DMA_enableChannel) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for DL_DMA_getTransferSize
    uart_callback.o(.text.BTBufferHandler) refers to board.o(.text.Systick_getTick) for Systick_getTick
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.bt_control) for bt_control
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.lastSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.tick) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleflag) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.BTBufferHandler.handleSize) for [Anonymous Symbol]
    uart_callback.o(.text.BTBufferHandler) refers to uart_callback.o(.bss.UART1Packet) for UART1Packet
    uart_callback.o(.ARM.exidx.text.BTBufferHandler) refers to uart_callback.o(.text.BTBufferHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize) refers to uart_callback.o(.text.DL_DMA_getTransferSize) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.bt_control) refers to uart_callback.o(.text.bt_control) for [Anonymous Symbol]
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.UART1Counts) for UART1Counts
    uart_callback.o(.text.UART1_IRQHandler) refers to uart_callback.o(.bss.UART1Packet) for UART1Packet
    uart_callback.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_callback.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to uart_callback.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    uart_callback.o(.ARM.exidx.text.DL_UART_receiveData) refers to uart_callback.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    initial.o(.text.Initial) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    initial.o(.text.Initial) refers to oled.o(.text.OLED_Init) for OLED_Init
    initial.o(.text.Initial) refers to uart_callback.o(.text.BT_DAMConfig) for BT_DAMConfig
    initial.o(.text.Initial) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    initial.o(.text.Initial) refers to oled.o(.text.OLED_Refresh_Gram) for OLED_Refresh_Gram
    initial.o(.text.Initial) refers to mpu6050.o(.text.MPU6050_Init) for MPU6050_Init
    initial.o(.text.Initial) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    initial.o(.text.Initial) refers to initial.o(.rodata.str1.1) for [Anonymous Symbol]
    initial.o(.ARM.exidx.text.Initial) refers to initial.o(.text.Initial) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Clear) refers to pid.o(.text.PID_Clear) for [Anonymous Symbol]
    pid.o(.text.PID) refers to track.o(.text.Track_Read) for Track_Read
    pid.o(.text.PID) refers to track.o(.text.Track_Process) for Track_Process
    pid.o(.text.PID) refers to track.o(.text.Track_PID_Update) for Track_PID_Update
    pid.o(.text.PID) refers to pid.o(.text.PID_Speed_Update) for PID_Speed_Update
    pid.o(.text.PID) refers to pid.o(.data.pid_type) for pid_type
    pid.o(.text.PID) refers to pid.o(.data.pid_left) for pid_left
    pid.o(.text.PID) refers to pid.o(.data.pid_right) for pid_right
    pid.o(.text.PID) refers to pid.o(.data.T_base_speed) for T_base_speed
    pid.o(.ARM.exidx.text.PID) refers to pid.o(.text.PID) for [Anonymous Symbol]
    pid.o(.text.PID_Speed_Update) refers to fflti.o(.text) for __aeabi_i2f
    pid.o(.text.PID_Speed_Update) refers to pid.o(.text.PID_Update_weifenxianxing) for PID_Update_weifenxianxing
    pid.o(.text.PID_Speed_Update) refers to ffixi.o(.text) for __aeabi_f2iz
    pid.o(.text.PID_Speed_Update) refers to motor.o(.text.Set_PWM) for Set_PWM
    pid.o(.text.PID_Speed_Update) refers to encoder.o(.bss.Get_Encoder_countA) for Get_Encoder_countA
    pid.o(.text.PID_Speed_Update) refers to encoder.o(.bss.Get_Encoder_countB) for Get_Encoder_countB
    pid.o(.text.PID_Speed_Update) refers to pid.o(.data.pid_left) for pid_left
    pid.o(.text.PID_Speed_Update) refers to pid.o(.data.pid_right) for pid_right
    pid.o(.ARM.exidx.text.PID_Speed_Update) refers to pid.o(.text.PID_Speed_Update) for [Anonymous Symbol]
    pid.o(.text.PID_Update_weifenxianxing) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.PID_Update_weifenxianxing) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    pid.o(.text.PID_Update_weifenxianxing) refers to f2d.o(.text) for __aeabi_f2d
    pid.o(.text.PID_Update_weifenxianxing) refers to dcmpge.o(.text) for __aeabi_dcmpge
    pid.o(.text.PID_Update_weifenxianxing) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.PID_Update_weifenxianxing) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.text.PID_Update_weifenxianxing) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.PID_Update_weifenxianxing) refers to pid.o(.text.PID_Update_weifenxianxing) for [Anonymous Symbol]
    pid.o(.text.PID_Update) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.PID_Update) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    pid.o(.text.PID_Update) refers to f2d.o(.text) for __aeabi_f2d
    pid.o(.text.PID_Update) refers to dcmpge.o(.text) for __aeabi_dcmpge
    pid.o(.text.PID_Update) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.PID_Update) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.text.PID_Update) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.PID_Update) refers to pid.o(.text.PID_Update) for [Anonymous Symbol]
    track.o(.text.Track_Read) refers to track.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    track.o(.text.Track_Read) refers to track.o(.bss.L2) for L2
    track.o(.text.Track_Read) refers to track.o(.bss.L1) for L1
    track.o(.text.Track_Read) refers to track.o(.bss.R1) for R1
    track.o(.text.Track_Read) refers to track.o(.bss.R2) for R2
    track.o(.text.Track_Read) refers to track.o(.bss.R3) for R3
    track.o(.ARM.exidx.text.Track_Read) refers to track.o(.text.Track_Read) for [Anonymous Symbol]
    track.o(.ARM.exidx.text.DL_GPIO_readPins) refers to track.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    track.o(.text.Track_Judge) refers to track.o(.text.Track_Read) for Track_Read
    track.o(.text.Track_Judge) refers to track.o(.bss.L2) for L2
    track.o(.text.Track_Judge) refers to track.o(.bss.L1) for L1
    track.o(.text.Track_Judge) refers to track.o(.bss.R1) for R1
    track.o(.text.Track_Judge) refers to track.o(.bss.R2) for R2
    track.o(.text.Track_Judge) refers to track.o(.bss.R3) for R3
    track.o(.text.Track_Judge) refers to track.o(.bss.track_state_black) for track_state_black
    track.o(.text.Track_Judge) refers to track.o(.bss.track_state_white) for track_state_white
    track.o(.text.Track_Judge) refers to track.o(.bss.track_state_all_black) for track_state_all_black
    track.o(.ARM.exidx.text.Track_Judge) refers to track.o(.text.Track_Judge) for [Anonymous Symbol]
    track.o(.text.Track_Process) refers to track.o(.data.Track_Process.last_R3) for [Anonymous Symbol]
    track.o(.text.Track_Process) refers to track.o(.bss.L2) for L2
    track.o(.text.Track_Process) refers to track.o(.bss.L1) for L1
    track.o(.text.Track_Process) refers to track.o(.bss.R1) for R1
    track.o(.text.Track_Process) refers to track.o(.bss.R2) for R2
    track.o(.text.Track_Process) refers to track.o(.bss.R3) for R3
    track.o(.text.Track_Process) refers to track.o(.data.Track_Process.last_L2) for [Anonymous Symbol]
    track.o(.text.Track_Process) refers to pid.o(.data.pid_track) for pid_track
    track.o(.ARM.exidx.text.Track_Process) refers to track.o(.text.Track_Process) for [Anonymous Symbol]
    track.o(.text.Track_PID_Update) refers to pid.o(.text.PID_Update) for PID_Update
    track.o(.text.Track_PID_Update) refers to fflti.o(.text) for __aeabi_i2f
    track.o(.text.Track_PID_Update) refers to fadd.o(.text) for __aeabi_fsub
    track.o(.text.Track_PID_Update) refers to ffixi.o(.text) for __aeabi_f2iz
    track.o(.text.Track_PID_Update) refers to pid.o(.data.pid_track) for pid_track
    track.o(.text.Track_PID_Update) refers to pid.o(.data.pid_left) for pid_left
    track.o(.text.Track_PID_Update) refers to pid.o(.data.pid_right) for pid_right
    track.o(.ARM.exidx.text.Track_PID_Update) refers to track.o(.text.Track_PID_Update) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (4096 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC12_VOLTAGE_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setUpperPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableFIFOs), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableLoopbackMode), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableDMAReceiveEvent), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSTICK_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing empty.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing empty.o(.data.Car_Mode), (1 bytes).
    Removing empty.o(.data.RC_Velocity), (4 bytes).
    Removing empty.o(.bss.Voltage), (4 bytes).
    Removing empty.o(.bss.Motor_Left), (4 bytes).
    Removing empty.o(.bss.Motor_Right), (4 bytes).
    Removing empty.o(.bss.PID_Send), (1 bytes).
    Removing empty.o(.bss.RC_Turn_Velocity), (4 bytes).
    Removing empty.o(.bss.Move_X), (4 bytes).
    Removing empty.o(.bss.Move_Y), (4 bytes).
    Removing empty.o(.bss.Move_Z), (4 bytes).
    Removing empty.o(.bss.PS2_ON_Flag), (4 bytes).
    Removing empty.o(.bss.Velocity_Left), (4 bytes).
    Removing empty.o(.bss.Velocity_Right), (4 bytes).
    Removing empty.o(.bss.test_num), (2 bytes).
    Removing empty.o(.bss.show_cnt), (2 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing board.o(.text), (0 bytes).
    Removing board.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing board.o(.ARM.exidx.text.TI_GetTick), (8 bytes).
    Removing board.o(.text.fputc), (44 bytes).
    Removing board.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing board.o(.text.DL_UART_isBusy), (24 bytes).
    Removing board.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing board.o(.text.fputs), (80 bytes).
    Removing board.o(.ARM.exidx.text.fputs), (8 bytes).
    Removing board.o(.text.puts), (48 bytes).
    Removing board.o(.ARM.exidx.text.puts), (8 bytes).
    Removing board.o(.ARM.exidx.text.Systick_getTick), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing board.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing board.o(.text.delay_1us), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1us), (8 bytes).
    Removing board.o(.text.delay_1ms), (16 bytes).
    Removing board.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing board.o(.rodata.str1.1), (2 bytes).
    Removing board.o(.bss.tick_ms), (4 bytes).
    Removing board.o(.bss.start_time), (4 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (52 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (36 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_disable), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (142 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setOversampling), (30 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setBaudRateDivisor), (76 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (64 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (74 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (32 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_isRXFIFOEmpty), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isRXFIFOEmpty), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveData), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_isTXFIFOFull), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_isTXFIFOFull), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitData), (22 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (58 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (70 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (200 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (228 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_enable), (22 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (256 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (276 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (88 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (128 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_sendCommand), (36 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_sendCommand), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setLoadValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCounterValueAfterEnable), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (280 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanConfig), (150 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCCPDirection), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (180 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (176 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getInChanPairConfig), (82 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getInChanPairConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (184 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (156 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (56 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (54 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (42 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (444 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (480 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (52 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (48 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (676 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (696 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (64 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.text.DL_SPI_init), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_setClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isRXFIFOEmpty), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isRXFIFOEmpty), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData8), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData16), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (32 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_isTXFIFOFull), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_isTXFIFOFull), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData8), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData16), (22 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitData32), (20 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitData32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (58 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (60 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (56 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (70 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (172 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (200 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_enable), (24 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_enable), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (72 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (216 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBinary), (32 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarHoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (176 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getClockFormat), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getClockFormat), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarSecondsBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarSecondsBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarHoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarHoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarDayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarYearBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarYearBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1HoursBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm1DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (134 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBinary), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2HoursBCD), (26 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (40 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (44 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (140 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBinary), (20 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2HoursBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (82 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBinary), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBinary), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2MinutesBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2MinutesBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2HoursBCD), (22 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2HoursBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (24 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfWeekBCD), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (28 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableAlarm2DayOfMonthBCD), (8 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (76 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_getGain), (32 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_getGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_setGain), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_setGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (80 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_FIELD32_RAW), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_FIELD32_RAW), (50 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_FIELD32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (388 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.HW_RD_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_RD_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.HW_WR_REG32_RAW), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.HW_WR_REG32_RAW), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessUnlock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessUnlock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeProtectedRegAccessLock), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeProtectedRegAccessLock), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (300 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccLoadRegister), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccLoadRegister), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (324 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (680 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (180 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getMsgObjSize), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getMsgObjSize), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsg), (320 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (304 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsg), (382 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsg), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (248 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (116 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (140 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (160 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (100 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (196 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (136 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (252 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (104 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (92 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (156 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (148 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (192 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (88 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (576 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (696 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getDataSize), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getDataSize), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (64 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandTwo), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandTwo), (8 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_setOperandOne), (24 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_setOperandOne), (8 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitControllerData), (22 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitControllerData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isControllerRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushControllerRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (84 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOFull), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOFull), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetData), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetTXFIFOEmpty), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetTXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetTXFIFO), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_startFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_startFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_isTargetRXFIFOEmpty), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_isTargetRXFIFOEmpty), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_stopFlushTargetRXFIFO), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_stopFlushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getTargetStatus), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getTargetStatus), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (60 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetData), (18 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetData), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (58 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setCommandAddress), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setCommandAddress), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (64 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_waitForCmdDone), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_waitForCmdDone), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getDATAFlashSize), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getDATAFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (378 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getNumBanks), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getNumBanks), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FactoryRegion_getMAINFlashSize), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FactoryRegion_getMAINFlashSize), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isFlashBankSwapEnabled), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isFlashBankSwapEnabled), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_enableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_enableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setBankSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setBankSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_setRegionSelect), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_setRegionSelect), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_disableAddressOverrideMode), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_disableAddressOverrideMode), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (60 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (38 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (148 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (348 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (146 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (158 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (156 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (178 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumber), (14 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumber), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_getFlashSectorNumberInBank), (82 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_getFlashSectorNumberInBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (4 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_SYSCTL_isExecuteFromUpperFlashBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (356 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (68 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (84 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32Config), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64Config), (56 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64Config), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (34 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (72 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (104 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_getInstructionConfig), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_getInstructionConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_CORE_configInstruction), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_CORE_configInstruction), (8 bytes).
    Removing dl_flashctl.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryConfig), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyConfig), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyConfig), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_configTransfer), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_setTrigger), (8 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (120 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_configDataFormat), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_configDataFormat), (8 bytes).
    Removing dl_dac12.o(.text.DL_Common_updateReg), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isFIFOFull), (34 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isFIFOFull), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output8), (22 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (40 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_output12), (28 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_output12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (70 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (72 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (32 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_startCalibration), (18 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_startCalibration), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_isCalibrationRunning), (30 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_isCalibrationRunning), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_getInterruptStatus), (24 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_getInterruptStatus), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (70 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData32), (24 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult32), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (64 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (82 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_setSeed16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_setSeed16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_feedData16), (36 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_feedData16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_getResult16), (20 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_getResult16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (72 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (64 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_checkAlignmentAndReturnPointer), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_checkAlignmentAndReturnPointer), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (80 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataWord), (66 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataWord), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (58 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (32 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (98 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (86 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (132 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (76 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (92 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (28 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Refresh_Gram), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WR_Byte), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RS_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Set), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SDIN_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SCLK_Set), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (34 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.oled_pow), (48 bytes).
    Removing oled.o(.ARM.exidx.text.oled_pow), (8 bytes).
    Removing oled.o(.text.OLED_ShowNumber), (220 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNumber), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Clr), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_RST_Set), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (60 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing oled.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.keyValue), (32 bytes).
    Removing key.o(.ARM.exidx.text.keyValue), (8 bytes).
    Removing key.o(.text.DL_GPIO_readPins), (22 bytes).
    Removing key.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing key.o(.text.key_scan), (388 bytes).
    Removing key.o(.ARM.exidx.text.key_scan), (8 bytes).
    Removing key.o(.bss.key_scan.time_core), (2 bytes).
    Removing key.o(.bss.key_scan.long_press_time), (2 bytes).
    Removing key.o(.bss.key_scan.press_flag), (1 bytes).
    Removing key.o(.bss.key_scan.check_once), (1 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_ON), (20 bytes).
    Removing led.o(.ARM.exidx.text.LED_ON), (8 bytes).
    Removing led.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing led.o(.text.LED_OFF), (20 bytes).
    Removing led.o(.ARM.exidx.text.LED_OFF), (8 bytes).
    Removing led.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing led.o(.text.LED_Toggle), (20 bytes).
    Removing led.o(.ARM.exidx.text.LED_Toggle), (8 bytes).
    Removing led.o(.text.DL_GPIO_togglePins), (24 bytes).
    Removing led.o(.ARM.exidx.text.DL_GPIO_togglePins), (8 bytes).
    Removing led.o(.text.LED_Flash), (68 bytes).
    Removing led.o(.ARM.exidx.text.LED_Flash), (8 bytes).
    Removing led.o(.bss.LED_Flash.temp), (2 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Set_PWM), (268 bytes).
    Removing motor.o(.ARM.exidx.text.Set_PWM), (8 bytes).
    Removing motor.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing motor.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing motor.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing datascope_dp.o(.text), (0 bytes).
    Removing datascope_dp.o(.text.Float2Byte), (64 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.Float2Byte), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Get_Channel_Data), (224 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Get_Channel_Data), (8 bytes).
    Removing datascope_dp.o(.text.DataScope_Data_Generate), (268 bytes).
    Removing datascope_dp.o(.ARM.exidx.text.DataScope_Data_Generate), (8 bytes).
    Removing datascope_dp.o(.bss.DataScope_OutPut_Buffer), (42 bytes).
    Removing mpu6050.o(.text), (0 bytes).
    Removing mpu6050.o(.ARM.exidx.text.Delay_Us), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_WriteReg), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ReadReg), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_Calibrate), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_GetData), (8 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_Init), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_GetID), (10 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_GetID), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_GetRealData), (372 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_GetRealData), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_GetCalibratedData), (136 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_GetCalibratedData), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_CalculateZAngle), (104 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_CalculateZAngle), (8 bytes).
    Removing mpu6050.o(.text.MPU6050_ZiTaiJieSuan), (44 bytes).
    Removing mpu6050.o(.ARM.exidx.text.MPU6050_ZiTaiJieSuan), (8 bytes).
    Removing mpu6050.o(.bss.Z_Angle), (4 bytes).
    Removing mpu6050.o(.bss.lastUpdateTime), (4 bytes).
    Removing myi2c.o(.text), (0 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_W_SCL), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_W_SDA), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_R_SDA), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing myi2c.o(.text.MyI2C_Init), (22 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_Init), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_Start), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_Stop), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_SendByte), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_ReceiveByte), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_SendAck), (8 bytes).
    Removing myi2c.o(.ARM.exidx.text.MyI2C_ReceiveAck), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.text.scheduler_enable_task), (84 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_enable_task), (8 bytes).
    Removing scheduler.o(.text.scheduler_disable_task), (84 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_disable_task), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing uart_callback.o(.text), (0 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BT_DAMConfig), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_disableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setSrcAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setDestAddr), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_setTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_enableChannel), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.BTBufferHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_DMA_getTransferSize), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.bt_control), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing uart_callback.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing uart_callback.o(.bss.lastUART1Counts), (1 bytes).
    Removing uart_callback.o(.bss.RecvOverFlag), (1 bytes).
    Removing uart_callback.o(.bss.UART1gCheck), (1 bytes).
    Removing initial.o(.text), (0 bytes).
    Removing initial.o(.ARM.exidx.text.Initial), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.PID_Clear), (22 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Clear), (8 bytes).
    Removing pid.o(.text.PID), (104 bytes).
    Removing pid.o(.ARM.exidx.text.PID), (8 bytes).
    Removing pid.o(.text.PID_Speed_Update), (116 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Speed_Update), (8 bytes).
    Removing pid.o(.text.PID_Update_weifenxianxing), (376 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Update_weifenxianxing), (8 bytes).
    Removing pid.o(.text.PID_Update), (374 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Update), (8 bytes).
    Removing pid.o(.data.pid_left), (56 bytes).
    Removing pid.o(.data.pid_right), (56 bytes).
    Removing pid.o(.data.pid_track), (56 bytes).
    Removing pid.o(.data.pid_type), (1 bytes).
    Removing pid.o(.data.T_base_speed), (2 bytes).
    Removing pid.o(.data.A_base_speed), (2 bytes).
    Removing interrupt.o(.text), (0 bytes).
    Removing track.o(.text), (0 bytes).
    Removing track.o(.ARM.exidx.text.Track_Read), (8 bytes).
    Removing track.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing track.o(.ARM.exidx.text.Track_Judge), (8 bytes).
    Removing track.o(.text.Track_Process), (380 bytes).
    Removing track.o(.ARM.exidx.text.Track_Process), (8 bytes).
    Removing track.o(.text.Track_PID_Update), (128 bytes).
    Removing track.o(.ARM.exidx.text.Track_PID_Update), (8 bytes).
    Removing track.o(.bss.track_state), (1 bytes).
    Removing track.o(.data.Track_Process.last_R3), (1 bytes).
    Removing track.o(.data.Track_Process.last_L2), (1 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (84 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing fadd.o(.text), (178 bytes).
    Removing fmul.o(.text), (122 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing fcmple.o(.text), (28 bytes).
    Removing fcmpge.o(.text), (28 bytes).
    Removing fcmpeq.o(.text), (28 bytes).
    Removing dcmpge.o(.text), (44 bytes).
    Removing fflti.o(.text), (22 bytes).
    Removing ffltui.o(.text), (14 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing f2d.o(.text), (40 bytes).
    Removing fepilogue.o(.text), (130 bytes).

1278 unused section(s) (total 48297 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpeq.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    DataScope_DP.C                           0x00000000   Number         0  datascope_dp.o ABSOLUTE
    Initial.c                                0x00000000   Number         0  initial.o ABSOLUTE
    MPU6050.c                                0x00000000   Number         0  mpu6050.o ABSOLUTE
    MyI2C.c                                  0x00000000   Number         0  myi2c.o ABSOLUTE
    PID.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    board.c                                  0x00000000   Number         0  board.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    interrupt.c                              0x00000000   Number         0  interrupt.o ABSOLUTE
    key.c                                    0x00000000   Number         0  key.o ABSOLUTE
    led.c                                    0x00000000   Number         0  led.o ABSOLUTE
    motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    track.c                                  0x00000000   Number         0  track.o ABSOLUTE
    uart_callback.c                          0x00000000   Number         0  uart_callback.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000128   Section        0  idiv_div0.o(.text)
    .text                                    0x00000178   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x000001a8   Section        0  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_1                             0x00000268   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_2                             0x0000026c   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_3                             0x00000270   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_4                             0x00000274   Number         4  uart_callback.o(.text.BTBufferHandler)
    __arm_cp.6_5                             0x00000278   Number         4  uart_callback.o(.text.BTBufferHandler)
    [Anonymous Symbol]                       0x0000027c   Section        0  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_0                             0x000002b8   Number         4  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_1                             0x000002bc   Number         4  uart_callback.o(.text.BT_DAMConfig)
    __arm_cp.0_2                             0x000002c0   Number         4  uart_callback.o(.text.BT_DAMConfig)
    DL_ADC12_clearInterruptStatus            0x000002c5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    [Anonymous Symbol]                       0x000002c4   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    __arm_cp.52_0                            0x000002dc   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus)
    DL_ADC12_configConversionMem             0x000002e1   Thumb Code    74  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    [Anonymous Symbol]                       0x000002e0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    DL_ADC12_enableConversions               0x0000032b   Thumb Code    22  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    [Anonymous Symbol]                       0x0000032a   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    DL_ADC12_enableInterrupt                 0x00000341   Thumb Code    24  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    [Anonymous Symbol]                       0x00000340   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    __arm_cp.53_0                            0x00000358   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt)
    DL_ADC12_enablePower                     0x0000035d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    [Anonymous Symbol]                       0x0000035c   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    __arm_cp.20_0                            0x00000370   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    DL_ADC12_initSingleSample                0x00000375   Thumb Code    60  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    [Anonymous Symbol]                       0x00000374   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_0                            0x000003b0   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_1                            0x000003b4   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_2                            0x000003b8   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.49_3                            0x000003bc   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    DL_ADC12_reset                           0x000003c1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x000003c0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    __arm_cp.16_0                            0x000003d0   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    __arm_cp.16_1                            0x000003d4   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x000003d8   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00000418   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x0000041c   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_ADC12_setSampleTime0                  0x00000421   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    [Anonymous Symbol]                       0x00000420   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    __arm_cp.51_0                            0x00000434   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    [Anonymous Symbol]                       0x00000438   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x0000044d   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x0000044c   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x00000475   Thumb Code    40  dl_uart.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000474   Section        0  dl_uart.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x0000049d   Thumb Code    40  dl_timer.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x0000049c   Section        0  dl_timer.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x000004c5   Thumb Code    40  dl_dma.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x000004c4   Section        0  dl_dma.o(.text.DL_Common_updateReg)
    DL_Common_updateReg                      0x000004ed   Thumb Code    40  dl_adc12.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x000004ec   Section        0  dl_adc12.o(.text.DL_Common_updateReg)
    DL_DMA_configTransfer                    0x00000515   Thumb Code    84  dl_dma.o(.text.DL_DMA_configTransfer)
    [Anonymous Symbol]                       0x00000514   Section        0  dl_dma.o(.text.DL_DMA_configTransfer)
    DL_DMA_disableChannel                    0x00000569   Thumb Code    38  uart_callback.o(.text.DL_DMA_disableChannel)
    [Anonymous Symbol]                       0x00000568   Section        0  uart_callback.o(.text.DL_DMA_disableChannel)
    DL_DMA_enableChannel                     0x0000058f   Thumb Code    38  uart_callback.o(.text.DL_DMA_enableChannel)
    [Anonymous Symbol]                       0x0000058e   Section        0  uart_callback.o(.text.DL_DMA_enableChannel)
    DL_DMA_getTransferSize                   0x000005b5   Thumb Code    32  uart_callback.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x000005b4   Section        0  uart_callback.o(.text.DL_DMA_getTransferSize)
    [Anonymous Symbol]                       0x000005d4   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    DL_DMA_setDestAddr                       0x0000061d   Thumb Code    36  uart_callback.o(.text.DL_DMA_setDestAddr)
    [Anonymous Symbol]                       0x0000061c   Section        0  uart_callback.o(.text.DL_DMA_setDestAddr)
    __arm_cp.3_0                             0x00000640   Number         4  uart_callback.o(.text.DL_DMA_setDestAddr)
    DL_DMA_setSrcAddr                        0x00000645   Thumb Code    36  uart_callback.o(.text.DL_DMA_setSrcAddr)
    [Anonymous Symbol]                       0x00000644   Section        0  uart_callback.o(.text.DL_DMA_setSrcAddr)
    __arm_cp.2_0                             0x00000668   Number         4  uart_callback.o(.text.DL_DMA_setSrcAddr)
    DL_DMA_setTransferSize                   0x0000066d   Thumb Code    44  uart_callback.o(.text.DL_DMA_setTransferSize)
    [Anonymous Symbol]                       0x0000066c   Section        0  uart_callback.o(.text.DL_DMA_setTransferSize)
    __arm_cp.4_0                             0x00000698   Number         4  uart_callback.o(.text.DL_DMA_setTransferSize)
    DL_DMA_setTrigger                        0x0000069d   Thumb Code    52  dl_dma.o(.text.DL_DMA_setTrigger)
    [Anonymous Symbol]                       0x0000069c   Section        0  dl_dma.o(.text.DL_DMA_setTrigger)
    __arm_cp.2_0                             0x000006d0   Number         4  dl_dma.o(.text.DL_DMA_setTrigger)
    DL_GPIO_clearInterruptStatus             0x000006d5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x000006d4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x000006ed   Thumb Code    24  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x000006ec   Section        0  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.3_0                             0x00000704   Number         4  encoder.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00000709   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000708   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x0000071d   Thumb Code    20  oled.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x0000071c   Section        0  oled.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00000731   Thumb Code    20  myi2c.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000730   Section        0  myi2c.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableInterrupt                  0x00000745   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x00000744   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.29_0                            0x0000075c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00000761   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000760   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.22_0                            0x00000774   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000779   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000778   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    __arm_cp.17_0                            0x0000078c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00000791   Thumb Code    20  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00000790   Section        0  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.1_0                             0x000007a4   Number         4  encoder.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInput                 0x000007a9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x000007a8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    __arm_cp.25_1                            0x000007bc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalOutput                0x000007c1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000007c0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initPeripheralInputFunction      0x000007d5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x000007d4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    __arm_cp.23_0                            0x000007ec   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralOutputFunction     0x000007f1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x000007f0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.21_0                            0x00000808   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x0000080d   Thumb Code    22  encoder.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x0000080c   Section        0  encoder.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00000823   Thumb Code    22  myi2c.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000822   Section        0  myi2c.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00000839   Thumb Code    22  track.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000838   Section        0  track.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000851   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000850   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_0                            0x00000860   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.13_1                            0x00000864   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x00000869   Thumb Code    20  oled.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000868   Section        0  oled.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000087d   Thumb Code    20  myi2c.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000087c   Section        0  myi2c.o(.text.DL_GPIO_setPins)
    __arm_cp.1_0                             0x00000890   Number         4  myi2c.o(.text.DL_GPIO_setPins)
    DL_GPIO_setUpperPinsPolarity             0x00000895   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x00000894   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setUpperPinsPolarity)
    [Anonymous Symbol]                       0x000008ac   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00000960   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000964   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000968   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x0000096d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x0000096c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00000979   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00000978   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.34_0                            0x00000988   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_setBORThreshold                0x0000098d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x0000098c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.30_0                            0x000009a0   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x000009a5   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x000009a4   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    DL_SYSCTL_setSYSOSCFreq                  0x000009c1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x000009c0   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.32_0                            0x000009d8   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x000009dd   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x000009dc   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x000009f4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000a14   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00000a18   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_SYSTICK_enable                        0x00000a1d   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    [Anonymous Symbol]                       0x00000a1c   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_enable)
    DL_SYSTICK_init                          0x00000a29   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00000a28   Section        0  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_0                            0x00000a44   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_1                            0x00000a48   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    __arm_cp.56_2                            0x00000a4c   Number         4  ti_msp_dl_config.o(.text.DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00000a50   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_enableClock                     0x00000b3d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00000b3c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    __arm_cp.37_0                            0x00000b4c   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enableInterrupt                 0x00000b51   Thumb Code    24  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    [Anonymous Symbol]                       0x00000b50   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    __arm_cp.39_0                            0x00000b68   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt)
    DL_Timer_enablePower                     0x00000b6d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000b6c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.18_0                            0x00000b80   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000b84   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.17_0                            0x00000c88   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.17_2                            0x00000c8c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00000c90   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_reset                           0x00000d8d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x00000d8c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x00000d9d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000d9c   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000db0   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000de0   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    __arm_cp.18_0                            0x00000e0c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    __arm_cp.18_1                            0x00000e10   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    [Anonymous Symbol]                       0x00000e14   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.6_0                             0x00000e48   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    __arm_cp.6_1                             0x00000e4c   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    [Anonymous Symbol]                       0x00000e50   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    [Anonymous Symbol]                       0x00000e84   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.19_0                            0x00000ec0   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000ec4   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.5_0                             0x00000eec   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000ef0   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterValueAfterEnable      0x00000f1d   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    [Anonymous Symbol]                       0x00000f1c   Section        0  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    __arm_cp.4_0                             0x00000f38   Number         4  dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable)
    DL_Timer_setLoadValue                    0x00000f3d   Thumb Code    20  dl_timer.o(.text.DL_Timer_setLoadValue)
    [Anonymous Symbol]                       0x00000f3c   Section        0  dl_timer.o(.text.DL_Timer_setLoadValue)
    __arm_cp.3_0                             0x00000f50   Number         4  dl_timer.o(.text.DL_Timer_setLoadValue)
    DL_UART_disable                          0x00000f55   Thumb Code    22  dl_uart.o(.text.DL_UART_disable)
    [Anonymous Symbol]                       0x00000f54   Section        0  dl_uart.o(.text.DL_UART_disable)
    DL_UART_enable                           0x00000f6b   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00000f6a   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableDMAReceiveEvent            0x00000f81   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    [Anonymous Symbol]                       0x00000f80   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    __arm_cp.48_0                            0x00000f94   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableDMAReceiveEvent)
    DL_UART_enableFIFOs                      0x00000f99   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    [Anonymous Symbol]                       0x00000f98   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableFIFOs)
    DL_UART_enableInterrupt                  0x00000fb1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00000fb0   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.47_0                            0x00000fc8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enableLoopbackMode               0x00000fcd   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode)
    [Anonymous Symbol]                       0x00000fcc   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableLoopbackMode)
    DL_UART_enablePower                      0x00000fe5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00000fe4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.19_0                            0x00000ff8   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00000ffd   Thumb Code    18  uart_callback.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000ffc   Section        0  uart_callback.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00001010   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001054   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_receiveData                      0x00001059   Thumb Code    16  uart_callback.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x00001058   Section        0  uart_callback.o(.text.DL_UART_receiveData)
    __arm_cp.11_0                            0x00001068   Number         4  uart_callback.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x0000106d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x0000106c   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_0                            0x0000107c   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.15_1                            0x00001080   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00001085   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001084   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_0                            0x000010c0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_1                            0x000010c4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_2                            0x000010c8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.41_3                            0x000010cc   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000010d0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    __arm_cp.3_0                             0x000010f0   Number         4  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000010f5   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000010f4   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_setRXFIFOThreshold               0x00001115   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    [Anonymous Symbol]                       0x00001114   Section        0  ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold)
    DL_UART_setTXFIFOThreshold               0x00001139   Thumb Code    36  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    [Anonymous Symbol]                       0x00001138   Section        0  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    __arm_cp.44_0                            0x0000115c   Number         4  ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold)
    [Anonymous Symbol]                       0x00001160   Section        0  mpu6050.o(.text.Delay_Us)
    [Anonymous Symbol]                       0x00001240   Section        0  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_1                             0x00001348   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_2                             0x0000134c   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_3                             0x00001350   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_4                             0x00001354   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.0_5                             0x00001358   Number         4  encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x0000135c   Section        0  initial.o(.text.Initial)
    __arm_cp.0_0                             0x00001388   Number         4  initial.o(.text.Initial)
    [Anonymous Symbol]                       0x0000138c   Section        0  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_0                             0x00001464   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_1                             0x00001468   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_2                             0x0000146c   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_3                             0x00001470   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_4                             0x00001474   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_5                             0x00001478   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    __arm_cp.3_6                             0x0000147c   Number         4  mpu6050.o(.text.MPU6050_Calibrate)
    [Anonymous Symbol]                       0x00001480   Section        0  mpu6050.o(.text.MPU6050_GetData)
    [Anonymous Symbol]                       0x00001558   Section        0  mpu6050.o(.text.MPU6050_Init)
    [Anonymous Symbol]                       0x00001596   Section        0  mpu6050.o(.text.MPU6050_ReadReg)
    [Anonymous Symbol]                       0x000015e4   Section        0  mpu6050.o(.text.MPU6050_WriteReg)
    [Anonymous Symbol]                       0x00001628   Section        0  myi2c.o(.text.MyI2C_R_SDA)
    __arm_cp.4_0                             0x00001638   Number         4  myi2c.o(.text.MyI2C_R_SDA)
    [Anonymous Symbol]                       0x0000163c   Section        0  myi2c.o(.text.MyI2C_ReceiveAck)
    [Anonymous Symbol]                       0x00001666   Section        0  myi2c.o(.text.MyI2C_ReceiveByte)
    [Anonymous Symbol]                       0x000016c6   Section        0  myi2c.o(.text.MyI2C_SendAck)
    [Anonymous Symbol]                       0x000016e6   Section        0  myi2c.o(.text.MyI2C_SendByte)
    [Anonymous Symbol]                       0x0000172e   Section        0  myi2c.o(.text.MyI2C_Start)
    [Anonymous Symbol]                       0x00001752   Section        0  myi2c.o(.text.MyI2C_Stop)
    [Anonymous Symbol]                       0x00001770   Section        0  myi2c.o(.text.MyI2C_W_SCL)
    [Anonymous Symbol]                       0x0000179c   Section        0  myi2c.o(.text.MyI2C_W_SDA)
    __arm_cp.3_0                             0x000017c8   Number         4  myi2c.o(.text.MyI2C_W_SDA)
    [Anonymous Symbol]                       0x000017cc   Section        0  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x00001828   Section        0  oled.o(.text.OLED_DrawPoint)
    [Anonymous Symbol]                       0x000018b4   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00001998   Section        0  oled.o(.text.OLED_RST_Clr)
    [Anonymous Symbol]                       0x000019a8   Section        0  oled.o(.text.OLED_RST_Set)
    [Anonymous Symbol]                       0x000019b8   Section        0  oled.o(.text.OLED_RS_Clr)
    [Anonymous Symbol]                       0x000019c8   Section        0  oled.o(.text.OLED_RS_Set)
    __arm_cp.2_0                             0x000019d8   Number         4  oled.o(.text.OLED_RS_Set)
    [Anonymous Symbol]                       0x000019dc   Section        0  oled.o(.text.OLED_Refresh_Gram)
    __arm_cp.0_0                             0x00001a54   Number         4  oled.o(.text.OLED_Refresh_Gram)
    [Anonymous Symbol]                       0x00001a58   Section        0  oled.o(.text.OLED_SCLK_Clr)
    [Anonymous Symbol]                       0x00001a68   Section        0  oled.o(.text.OLED_SCLK_Set)
    [Anonymous Symbol]                       0x00001a78   Section        0  oled.o(.text.OLED_SDIN_Clr)
    [Anonymous Symbol]                       0x00001a88   Section        0  oled.o(.text.OLED_SDIN_Set)
    __arm_cp.5_0                             0x00001a98   Number         4  oled.o(.text.OLED_SDIN_Set)
    [Anonymous Symbol]                       0x00001a9c   Section        0  oled.o(.text.OLED_ShowChar)
    __arm_cp.12_0                            0x00001ba8   Number         4  oled.o(.text.OLED_ShowChar)
    __arm_cp.12_1                            0x00001bac   Number         4  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00001bb0   Section        0  oled.o(.text.OLED_ShowString)
    [Anonymous Symbol]                       0x00001c2a   Section        0  oled.o(.text.OLED_WR_Byte)
    [Anonymous Symbol]                       0x00001c98   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    __arm_cp.8_0                             0x00001cf8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    __arm_cp.8_1                             0x00001cfc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    [Anonymous Symbol]                       0x00001d00   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.55_0                            0x00001d10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.55_1                            0x00001d14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x00001d18   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x00001d20   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001e40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00001e44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00001e48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001e4c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_0                             0x00001ec0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    __arm_cp.4_2                             0x00001ec4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    [Anonymous Symbol]                       0x00001ec8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001f0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001f10   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001f20   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_0                             0x00001f48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.5_2                             0x00001f4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00001f50   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_0                             0x00001fa0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_2                             0x00001fa4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001fa8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.7_0                             0x00001ffc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.7_2                             0x00002000   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00002004   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00002038   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x0000203c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000020ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000020b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000020b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000020b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x000020bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x000020c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x000020c4   Section        0  board.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x000020d0   Section        0  board.o(.text.Systick_getTick)
    __arm_cp.6_0                             0x000020d8   Number         4  board.o(.text.Systick_getTick)
    [Anonymous Symbol]                       0x000020dc   Section        0  board.o(.text.TI_GetTick)
    __arm_cp.1_0                             0x000020e4   Number         4  board.o(.text.TI_GetTick)
    [Anonymous Symbol]                       0x000020e8   Section        0  track.o(.text.Track_Judge)
    __arm_cp.2_5                             0x000021a0   Number         4  track.o(.text.Track_Judge)
    __arm_cp.2_6                             0x000021a4   Number         4  track.o(.text.Track_Judge)
    __arm_cp.2_7                             0x000021a8   Number         4  track.o(.text.Track_Judge)
    [Anonymous Symbol]                       0x000021ac   Section        0  track.o(.text.Track_Read)
    __arm_cp.0_0                             0x00002210   Number         4  track.o(.text.Track_Read)
    __arm_cp.0_1                             0x00002214   Number         4  track.o(.text.Track_Read)
    __arm_cp.0_2                             0x00002218   Number         4  track.o(.text.Track_Read)
    __arm_cp.0_3                             0x0000221c   Number         4  track.o(.text.Track_Read)
    __arm_cp.0_4                             0x00002220   Number         4  track.o(.text.Track_Read)
    __arm_cp.0_5                             0x00002224   Number         4  track.o(.text.Track_Read)
    [Anonymous Symbol]                       0x00002228   Section        0  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_0                             0x00002278   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_1                             0x0000227c   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __arm_cp.9_2                             0x00002280   Number         4  uart_callback.o(.text.UART1_IRQHandler)
    __NVIC_ClearPendingIRQ                   0x00002285   Thumb Code    40  empty.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00002284   Section        0  empty.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x000022ac   Number         4  empty.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x000022b1   Thumb Code    40  empty.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000022b0   Section        0  empty.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x000022d8   Number         4  empty.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x000022dd   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000022dc   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.36_0                            0x00002358   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.36_1                            0x0000235c   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00002360   Section        0  uart_callback.o(.text.bt_control)
    [Anonymous Symbol]                       0x0000236c   Section        0  board.o(.text.delay_ms)
    __arm_cp.7_0                             0x00002398   Number         4  board.o(.text.delay_ms)
    [Anonymous Symbol]                       0x0000239c   Section        0  board.o(.text.delay_us)
    __arm_cp.8_0                             0x00002420   Number         4  board.o(.text.delay_us)
    __arm_cp.8_1                             0x00002424   Number         4  board.o(.text.delay_us)
    __arm_cp.8_2                             0x00002428   Number         4  board.o(.text.delay_us)
    [Anonymous Symbol]                       0x0000242c   Section        0  empty.o(.text.main)
    [Anonymous Symbol]                       0x00002490   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x00002498   Section        0  scheduler.o(.text.scheduler_run)
    __arm_cp.3_0                             0x0000250c   Number         4  scheduler.o(.text.scheduler_run)
    __arm_cp.3_1                             0x00002510   Number         4  scheduler.o(.text.scheduler_run)
    i.__scatterload_copy                     0x00002518   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00002528   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00002530   Section       14  handlers.o(i.__scatterload_zeroinit)
    gADC12_VOLTAGEClockConfig                0x00002540   Data           8  ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig)
    [Anonymous Symbol]                       0x00002540   Section        0  ti_msp_dl_config.o(.rodata.gADC12_VOLTAGEClockConfig)
    gDMA_CH0Config                           0x00002548   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x00002548   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gPWM_0ClockConfig                        0x00002560   Data           3  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    [Anonymous Symbol]                       0x00002560   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0ClockConfig)
    gPWM_0Config                             0x00002564   Data           8  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    [Anonymous Symbol]                       0x00002564   Section        0  ti_msp_dl_config.o(.rodata.gPWM_0Config)
    gSYSPLLConfig                            0x0000256c   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x0000256c   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gTIMER_0ClockConfig                      0x00002594   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00002594   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00002598   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00002598   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x000025ac   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000025ac   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000025ae   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000025ae   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x000025b8   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x000025b8   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x000025ba   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x000025ba   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x00003028   Section        0  initial.o(.rodata.str1.1)
    scheduler_task                           0x20200000   Data          48  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20200000   Section        0  scheduler.o(.data.scheduler_task)
    BTBufferHandler.handleSize               0x20200036   Data           1  uart_callback.o(.bss.BTBufferHandler.handleSize)
    [Anonymous Symbol]                       0x20200036   Section        0  uart_callback.o(.bss.BTBufferHandler.handleSize)
    BTBufferHandler.handleflag               0x20200037   Data           1  uart_callback.o(.bss.BTBufferHandler.handleflag)
    [Anonymous Symbol]                       0x20200037   Section        0  uart_callback.o(.bss.BTBufferHandler.handleflag)
    BTBufferHandler.lastSize                 0x20200038   Data           1  uart_callback.o(.bss.BTBufferHandler.lastSize)
    [Anonymous Symbol]                       0x20200038   Section        0  uart_callback.o(.bss.BTBufferHandler.lastSize)
    BTBufferHandler.tick                     0x2020003c   Data           4  uart_callback.o(.bss.BTBufferHandler.tick)
    [Anonymous Symbol]                       0x2020003c   Section        0  uart_callback.o(.bss.BTBufferHandler.tick)
    STACK                                    0x202005f0   Section     4096  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_uidiv                            0x000000e9   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x000000e9   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_idiv                             0x00000129   Thumb Code     0  idiv_div0.o(.text)
    __aeabi_idivmod$div0                     0x00000129   Thumb Code    74  idiv_div0.o(.text)
    __scatterload                            0x00000179   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000179   Thumb Code     0  init.o(.text)
    BTBufferHandler                          0x000001a9   Thumb Code   192  uart_callback.o(.text.BTBufferHandler)
    BT_DAMConfig                             0x0000027d   Thumb Code    60  uart_callback.o(.text.BT_DAMConfig)
    DL_ADC12_setClockConfig                  0x000003d9   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00000439   Thumb Code    20  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x000005d5   Thumb Code    70  dl_dma.o(.text.DL_DMA_initChannel)
    DL_SYSCTL_configSYSPLL                   0x000008ad   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x000009f5   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_TimerA_initPWMMode                    0x00000a51   Thumb Code   234  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x00000b85   Thumb Code   260  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00000c91   Thumb Code   252  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000db1   Thumb Code    48  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareAction         0x00000de1   Thumb Code    44  dl_timer.o(.text.DL_Timer_setCaptureCompareAction)
    DL_Timer_setCaptureCompareCtl            0x00000e15   Thumb Code    52  dl_timer.o(.text.DL_Timer_setCaptureCompareCtl)
    DL_Timer_setCaptureCompareInput          0x00000e51   Thumb Code    52  dl_timer.o(.text.DL_Timer_setCaptureCompareInput)
    DL_Timer_setCaptureCompareOutCtl         0x00000e85   Thumb Code    60  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000ec5   Thumb Code    40  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000ef1   Thumb Code    44  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00001011   Thumb Code    68  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000010d1   Thumb Code    32  dl_uart.o(.text.DL_UART_setClockConfig)
    Delay_Us                                 0x00001161   Thumb Code   224  mpu6050.o(.text.Delay_Us)
    GROUP1_IRQHandler                        0x00001241   Thumb Code   264  encoder.o(.text.GROUP1_IRQHandler)
    Initial                                  0x0000135d   Thumb Code    44  initial.o(.text.Initial)
    MPU6050_Calibrate                        0x0000138d   Thumb Code   216  mpu6050.o(.text.MPU6050_Calibrate)
    MPU6050_GetData                          0x00001481   Thumb Code   216  mpu6050.o(.text.MPU6050_GetData)
    MPU6050_Init                             0x00001559   Thumb Code    62  mpu6050.o(.text.MPU6050_Init)
    MPU6050_ReadReg                          0x00001597   Thumb Code    78  mpu6050.o(.text.MPU6050_ReadReg)
    MPU6050_WriteReg                         0x000015e5   Thumb Code    68  mpu6050.o(.text.MPU6050_WriteReg)
    MyI2C_R_SDA                              0x00001629   Thumb Code    16  myi2c.o(.text.MyI2C_R_SDA)
    MyI2C_ReceiveAck                         0x0000163d   Thumb Code    42  myi2c.o(.text.MyI2C_ReceiveAck)
    MyI2C_ReceiveByte                        0x00001667   Thumb Code    96  myi2c.o(.text.MyI2C_ReceiveByte)
    MyI2C_SendAck                            0x000016c7   Thumb Code    32  myi2c.o(.text.MyI2C_SendAck)
    MyI2C_SendByte                           0x000016e7   Thumb Code    72  myi2c.o(.text.MyI2C_SendByte)
    MyI2C_Start                              0x0000172f   Thumb Code    36  myi2c.o(.text.MyI2C_Start)
    MyI2C_Stop                               0x00001753   Thumb Code    28  myi2c.o(.text.MyI2C_Stop)
    MyI2C_W_SCL                              0x00001771   Thumb Code    44  myi2c.o(.text.MyI2C_W_SCL)
    MyI2C_W_SDA                              0x0000179d   Thumb Code    44  myi2c.o(.text.MyI2C_W_SDA)
    OLED_Clear                               0x000017cd   Thumb Code    92  oled.o(.text.OLED_Clear)
    OLED_DrawPoint                           0x00001829   Thumb Code   140  oled.o(.text.OLED_DrawPoint)
    OLED_Init                                0x000018b5   Thumb Code   228  oled.o(.text.OLED_Init)
    OLED_RST_Clr                             0x00001999   Thumb Code    16  oled.o(.text.OLED_RST_Clr)
    OLED_RST_Set                             0x000019a9   Thumb Code    16  oled.o(.text.OLED_RST_Set)
    OLED_RS_Clr                              0x000019b9   Thumb Code    16  oled.o(.text.OLED_RS_Clr)
    OLED_RS_Set                              0x000019c9   Thumb Code    16  oled.o(.text.OLED_RS_Set)
    OLED_Refresh_Gram                        0x000019dd   Thumb Code   120  oled.o(.text.OLED_Refresh_Gram)
    OLED_SCLK_Clr                            0x00001a59   Thumb Code    16  oled.o(.text.OLED_SCLK_Clr)
    OLED_SCLK_Set                            0x00001a69   Thumb Code    16  oled.o(.text.OLED_SCLK_Set)
    OLED_SDIN_Clr                            0x00001a79   Thumb Code    16  oled.o(.text.OLED_SDIN_Clr)
    OLED_SDIN_Set                            0x00001a89   Thumb Code    16  oled.o(.text.OLED_SDIN_Set)
    OLED_ShowChar                            0x00001a9d   Thumb Code   268  oled.o(.text.OLED_ShowChar)
    OLED_ShowString                          0x00001bb1   Thumb Code   122  oled.o(.text.OLED_ShowString)
    OLED_WR_Byte                             0x00001c2b   Thumb Code   110  oled.o(.text.OLED_WR_Byte)
    SYSCFG_DL_ADC12_VOLTAGE_init             0x00001c99   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_VOLTAGE_init)
    SYSCFG_DL_DMA_CH0_init                   0x00001d01   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_init                       0x00001d19   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x00001d21   Thumb Code   288  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_PWM_0_init                     0x00001e4d   Thumb Code   116  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init)
    SYSCFG_DL_SYSCTL_init                    0x00001ec9   Thumb Code    68  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001f11   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x00001f21   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00001f51   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00001fa9   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_init                           0x00002005   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x0000203d   Thumb Code   112  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x000020c5   Thumb Code    12  board.o(.text.SysTick_Handler)
    Systick_getTick                          0x000020d1   Thumb Code     8  board.o(.text.Systick_getTick)
    TI_GetTick                               0x000020dd   Thumb Code     8  board.o(.text.TI_GetTick)
    Track_Judge                              0x000020e9   Thumb Code   184  track.o(.text.Track_Judge)
    Track_Read                               0x000021ad   Thumb Code   100  track.o(.text.Track_Read)
    UART1_IRQHandler                         0x00002229   Thumb Code    80  uart_callback.o(.text.UART1_IRQHandler)
    bt_control                               0x00002361   Thumb Code    10  uart_callback.o(.text.bt_control)
    delay_ms                                 0x0000236d   Thumb Code    44  board.o(.text.delay_ms)
    delay_us                                 0x0000239d   Thumb Code   132  board.o(.text.delay_us)
    main                                     0x0000242d   Thumb Code    98  empty.o(.text.main)
    scheduler_init                           0x00002491   Thumb Code     8  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x00002499   Thumb Code   116  scheduler.o(.text.scheduler_run)
    __scatterload_copy                       0x00002519   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00002529   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00002531   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    oled_asc2_1206                           0x000025c4   Data        1140  oled.o(.rodata.oled_asc2_1206)
    oled_asc2_1608                           0x00002a38   Data        1520  oled.o(.rodata.oled_asc2_1608)
    Region$$Table$$Base                      0x00003030   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00003050   Number         0  anon$$obj.o(Region$$Table)
    AccX_offset                              0x20200030   Data           2  mpu6050.o(.bss.AccX_offset)
    AccY_offset                              0x20200032   Data           2  mpu6050.o(.bss.AccY_offset)
    AccZ_offset                              0x20200034   Data           2  mpu6050.o(.bss.AccZ_offset)
    Get_Encoder_countA                       0x20200040   Data           4  encoder.o(.bss.Get_Encoder_countA)
    Get_Encoder_countB                       0x20200044   Data           4  encoder.o(.bss.Get_Encoder_countB)
    GyroX_offset                             0x20200048   Data           2  mpu6050.o(.bss.GyroX_offset)
    GyroY_offset                             0x2020004a   Data           2  mpu6050.o(.bss.GyroY_offset)
    GyroZ_offset                             0x2020004c   Data           2  mpu6050.o(.bss.GyroZ_offset)
    L1                                       0x2020004e   Data           1  track.o(.bss.L1)
    L2                                       0x2020004f   Data           1  track.o(.bss.L2)
    OLED_GRAM                                0x20200050   Data        1024  oled.o(.bss.OLED_GRAM)
    R1                                       0x20200450   Data           1  track.o(.bss.R1)
    R2                                       0x20200451   Data           1  track.o(.bss.R2)
    R3                                       0x20200452   Data           1  track.o(.bss.R3)
    UART1Counts                              0x20200453   Data           1  uart_callback.o(.bss.UART1Counts)
    UART1Packet                              0x20200454   Data         200  uart_callback.o(.bss.UART1Packet)
    gPWM_0Backup                             0x2020051c   Data         188  ti_msp_dl_config.o(.bss.gPWM_0Backup)
    g_msTicks                                0x202005d8   Data           4  board.o(.bss.g_msTicks)
    gpio_interrup1                           0x202005dc   Data           4  encoder.o(.bss.gpio_interrup1)
    gpio_interrup2                           0x202005e0   Data           4  encoder.o(.bss.gpio_interrup2)
    task_num                                 0x202005e4   Data           1  scheduler.o(.bss.task_num)
    track_state_all_black                    0x202005e6   Data           2  track.o(.bss.track_state_all_black)
    track_state_black                        0x202005e8   Data           2  track.o(.bss.track_state_black)
    track_state_white                        0x202005ea   Data           2  track.o(.bss.track_state_white)
    __initial_sp                             0x202015f0   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00003080, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00003050, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO            3    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1676  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         1712    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         1715    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1717    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1719    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         1720    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1722    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1724    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         1713    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO            4    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x0000003e   Code   RO         1730    .text               mc_p.l(uidiv_div0.o)
    0x00000126   0x00000126   0x00000002   PAD
    0x00000128   0x00000128   0x00000050   Code   RO         1732    .text               mc_p.l(idiv_div0.o)
    0x00000178   0x00000178   0x00000030   Code   RO         1739    .text               mc_p.l(init.o)
    0x000001a8   0x000001a8   0x000000d4   Code   RO         1556    .text.BTBufferHandler  uart_callback.o
    0x0000027c   0x0000027c   0x00000048   Code   RO         1544    .text.BT_DAMConfig  uart_callback.o
    0x000002c4   0x000002c4   0x0000001c   Code   RO          115    .text.DL_ADC12_clearInterruptStatus  ti_msp_dl_config.o
    0x000002e0   0x000002e0   0x0000004a   Code   RO          111    .text.DL_ADC12_configConversionMem  ti_msp_dl_config.o
    0x0000032a   0x0000032a   0x00000016   Code   RO          119    .text.DL_ADC12_enableConversions  ti_msp_dl_config.o
    0x00000340   0x00000340   0x0000001c   Code   RO          117    .text.DL_ADC12_enableInterrupt  ti_msp_dl_config.o
    0x0000035c   0x0000035c   0x00000018   Code   RO           51    .text.DL_ADC12_enablePower  ti_msp_dl_config.o
    0x00000374   0x00000374   0x0000004c   Code   RO          109    .text.DL_ADC12_initSingleSample  ti_msp_dl_config.o
    0x000003c0   0x000003c0   0x00000018   Code   RO           43    .text.DL_ADC12_reset  ti_msp_dl_config.o
    0x000003d8   0x000003d8   0x00000048   Code   RO         1293    .text.DL_ADC12_setClockConfig  dl_adc12.o
    0x00000420   0x00000420   0x00000018   Code   RO          113    .text.DL_ADC12_setSampleTime0  ti_msp_dl_config.o
    0x00000438   0x00000438   0x00000014   Code   RO         1245    .text.DL_Common_delayCycles  dl_common.o
    0x0000044c   0x0000044c   0x00000028   Code   RO          127    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00000474   0x00000474   0x00000028   Code   RO          227    .text.DL_Common_updateReg  dl_uart.o
    0x0000049c   0x0000049c   0x00000028   Code   RO          317    .text.DL_Common_updateReg  dl_timer.o
    0x000004c4   0x000004c4   0x00000028   Code   RO         1173    .text.DL_Common_updateReg  dl_dma.o
    0x000004ec   0x000004ec   0x00000028   Code   RO         1295    .text.DL_Common_updateReg  dl_adc12.o
    0x00000514   0x00000514   0x00000054   Code   RO         1169    .text.DL_DMA_configTransfer  dl_dma.o
    0x00000568   0x00000568   0x00000026   Code   RO         1546    .text.DL_DMA_disableChannel  uart_callback.o
    0x0000058e   0x0000058e   0x00000026   Code   RO         1554    .text.DL_DMA_enableChannel  uart_callback.o
    0x000005b4   0x000005b4   0x00000020   Code   RO         1558    .text.DL_DMA_getTransferSize  uart_callback.o
    0x000005d4   0x000005d4   0x00000046   Code   RO         1167    .text.DL_DMA_initChannel  dl_dma.o
    0x0000061a   0x0000061a   0x00000002   PAD
    0x0000061c   0x0000061c   0x00000028   Code   RO         1550    .text.DL_DMA_setDestAddr  uart_callback.o
    0x00000644   0x00000644   0x00000028   Code   RO         1548    .text.DL_DMA_setSrcAddr  uart_callback.o
    0x0000066c   0x0000066c   0x00000030   Code   RO         1552    .text.DL_DMA_setTransferSize  uart_callback.o
    0x0000069c   0x0000069c   0x00000038   Code   RO         1171    .text.DL_DMA_setTrigger  dl_dma.o
    0x000006d4   0x000006d4   0x00000018   Code   RO           67    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x000006ec   0x000006ec   0x0000001c   Code   RO         1430    .text.DL_GPIO_clearInterruptStatus  encoder.o
    0x00000708   0x00000708   0x00000014   Code   RO           63    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x0000071c   0x0000071c   0x00000014   Code   RO         1358    .text.DL_GPIO_clearPins  oled.o
    0x00000730   0x00000730   0x00000014   Code   RO         1498    .text.DL_GPIO_clearPins  myi2c.o
    0x00000744   0x00000744   0x0000001c   Code   RO           69    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00000760   0x00000760   0x00000018   Code   RO           55    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000778   0x00000778   0x00000018   Code   RO           45    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000790   0x00000790   0x00000018   Code   RO         1426    .text.DL_GPIO_getEnabledInterruptStatus  encoder.o
    0x000007a8   0x000007a8   0x00000018   Code   RO           61    .text.DL_GPIO_initDigitalInput  ti_msp_dl_config.o
    0x000007c0   0x000007c0   0x00000014   Code   RO           59    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x000007d4   0x000007d4   0x0000001c   Code   RO           57    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x000007f0   0x000007f0   0x0000001c   Code   RO           53    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x0000080c   0x0000080c   0x00000016   Code   RO         1428    .text.DL_GPIO_readPins  encoder.o
    0x00000822   0x00000822   0x00000016   Code   RO         1504    .text.DL_GPIO_readPins  myi2c.o
    0x00000838   0x00000838   0x00000016   Code   RO         1619    .text.DL_GPIO_readPins  track.o
    0x0000084e   0x0000084e   0x00000002   PAD
    0x00000850   0x00000850   0x00000018   Code   RO           37    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000868   0x00000868   0x00000014   Code   RO         1360    .text.DL_GPIO_setPins  oled.o
    0x0000087c   0x0000087c   0x00000018   Code   RO         1496    .text.DL_GPIO_setPins  myi2c.o
    0x00000894   0x00000894   0x00000018   Code   RO           65    .text.DL_GPIO_setUpperPinsPolarity  ti_msp_dl_config.o
    0x000008ac   0x000008ac   0x000000c0   Code   RO         1645    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x0000096c   0x0000096c   0x0000000c   Code   RO           77    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00000978   0x00000978   0x00000014   Code   RO           79    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x0000098c   0x0000098c   0x00000018   Code   RO           71    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x000009a4   0x000009a4   0x0000001c   Code   RO           73    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x000009c0   0x000009c0   0x0000001c   Code   RO           75    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x000009dc   0x000009dc   0x00000018   Code   RO           81    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x000009f4   0x000009f4   0x00000028   Code   RO         1653    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000a1c   0x00000a1c   0x0000000c   Code   RO          125    .text.DL_SYSTICK_enable  ti_msp_dl_config.o
    0x00000a28   0x00000a28   0x00000028   Code   RO          123    .text.DL_SYSTICK_init  ti_msp_dl_config.o
    0x00000a50   0x00000a50   0x000000ea   Code   RO          369    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x00000b3a   0x00000b3a   0x00000002   PAD
    0x00000b3c   0x00000b3c   0x00000014   Code   RO           85    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00000b50   0x00000b50   0x0000001c   Code   RO           89    .text.DL_Timer_enableInterrupt  ti_msp_dl_config.o
    0x00000b6c   0x00000b6c   0x00000018   Code   RO           47    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00000b84   0x00000b84   0x0000010c   Code   RO          329    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00000c90   0x00000c90   0x000000fc   Code   RO          299    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00000d8c   0x00000d8c   0x00000010   Code   RO           39    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x00000d9c   0x00000d9c   0x00000014   Code   RO           87    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00000db0   0x00000db0   0x00000030   Code   RO          343    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x00000de0   0x00000de0   0x00000034   Code   RO          331    .text.DL_Timer_setCaptureCompareAction  dl_timer.o
    0x00000e14   0x00000e14   0x0000003c   Code   RO          307    .text.DL_Timer_setCaptureCompareCtl  dl_timer.o
    0x00000e50   0x00000e50   0x00000034   Code   RO          313    .text.DL_Timer_setCaptureCompareInput  dl_timer.o
    0x00000e84   0x00000e84   0x00000040   Code   RO          333    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x00000ec4   0x00000ec4   0x0000002c   Code   RO          305    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x00000ef0   0x00000ef0   0x0000002c   Code   RO          295    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00000f1c   0x00000f1c   0x00000020   Code   RO          303    .text.DL_Timer_setCounterValueAfterEnable  dl_timer.o
    0x00000f3c   0x00000f3c   0x00000018   Code   RO          301    .text.DL_Timer_setLoadValue  dl_timer.o
    0x00000f54   0x00000f54   0x00000016   Code   RO          225    .text.DL_UART_disable  dl_uart.o
    0x00000f6a   0x00000f6a   0x00000016   Code   RO          103    .text.DL_UART_enable  ti_msp_dl_config.o
    0x00000f80   0x00000f80   0x00000018   Code   RO          107    .text.DL_UART_enableDMAReceiveEvent  ti_msp_dl_config.o
    0x00000f98   0x00000f98   0x00000018   Code   RO           95    .text.DL_UART_enableFIFOs  ti_msp_dl_config.o
    0x00000fb0   0x00000fb0   0x0000001c   Code   RO          105    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00000fcc   0x00000fcc   0x00000016   Code   RO          101    .text.DL_UART_enableLoopbackMode  ti_msp_dl_config.o
    0x00000fe2   0x00000fe2   0x00000002   PAD
    0x00000fe4   0x00000fe4   0x00000018   Code   RO           49    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00000ffc   0x00000ffc   0x00000012   Code   RO         1564    .text.DL_UART_getPendingInterrupt  uart_callback.o
    0x0000100e   0x0000100e   0x00000002   PAD
    0x00001010   0x00001010   0x00000048   Code   RO          223    .text.DL_UART_init  dl_uart.o
    0x00001058   0x00001058   0x00000014   Code   RO         1566    .text.DL_UART_receiveData  uart_callback.o
    0x0000106c   0x0000106c   0x00000018   Code   RO           41    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00001084   0x00001084   0x0000004c   Code   RO           93    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000010d0   0x000010d0   0x00000024   Code   RO          229    .text.DL_UART_setClockConfig  dl_uart.o
    0x000010f4   0x000010f4   0x0000001e   Code   RO           91    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00001112   0x00001112   0x00000002   PAD
    0x00001114   0x00001114   0x00000024   Code   RO           97    .text.DL_UART_setRXFIFOThreshold  ti_msp_dl_config.o
    0x00001138   0x00001138   0x00000028   Code   RO           99    .text.DL_UART_setTXFIFOThreshold  ti_msp_dl_config.o
    0x00001160   0x00001160   0x000000e0   Code   RO         1457    .text.Delay_Us      mpu6050.o
    0x00001240   0x00001240   0x0000011c   Code   RO         1424    .text.GROUP1_IRQHandler  encoder.o
    0x0000135c   0x0000135c   0x00000030   Code   RO         1584    .text.Initial       initial.o
    0x0000138c   0x0000138c   0x000000f4   Code   RO         1463    .text.MPU6050_Calibrate  mpu6050.o
    0x00001480   0x00001480   0x000000d8   Code   RO         1465    .text.MPU6050_GetData  mpu6050.o
    0x00001558   0x00001558   0x0000003e   Code   RO         1467    .text.MPU6050_Init  mpu6050.o
    0x00001596   0x00001596   0x0000004e   Code   RO         1461    .text.MPU6050_ReadReg  mpu6050.o
    0x000015e4   0x000015e4   0x00000044   Code   RO         1459    .text.MPU6050_WriteReg  mpu6050.o
    0x00001628   0x00001628   0x00000014   Code   RO         1502    .text.MyI2C_R_SDA   myi2c.o
    0x0000163c   0x0000163c   0x0000002a   Code   RO         1518    .text.MyI2C_ReceiveAck  myi2c.o
    0x00001666   0x00001666   0x00000060   Code   RO         1514    .text.MyI2C_ReceiveByte  myi2c.o
    0x000016c6   0x000016c6   0x00000020   Code   RO         1516    .text.MyI2C_SendAck  myi2c.o
    0x000016e6   0x000016e6   0x00000048   Code   RO         1512    .text.MyI2C_SendByte  myi2c.o
    0x0000172e   0x0000172e   0x00000024   Code   RO         1508    .text.MyI2C_Start   myi2c.o
    0x00001752   0x00001752   0x0000001c   Code   RO         1510    .text.MyI2C_Stop    myi2c.o
    0x0000176e   0x0000176e   0x00000002   PAD
    0x00001770   0x00001770   0x0000002c   Code   RO         1494    .text.MyI2C_W_SCL   myi2c.o
    0x0000179c   0x0000179c   0x00000030   Code   RO         1500    .text.MyI2C_W_SDA   myi2c.o
    0x000017cc   0x000017cc   0x0000005c   Code   RO         1338    .text.OLED_Clear    oled.o
    0x00001828   0x00001828   0x0000008c   Code   RO         1340    .text.OLED_DrawPoint  oled.o
    0x000018b4   0x000018b4   0x000000e4   Code   RO         1350    .text.OLED_Init     oled.o
    0x00001998   0x00001998   0x00000010   Code   RO         1352    .text.OLED_RST_Clr  oled.o
    0x000019a8   0x000019a8   0x00000010   Code   RO         1354    .text.OLED_RST_Set  oled.o
    0x000019b8   0x000019b8   0x00000010   Code   RO         1324    .text.OLED_RS_Clr   oled.o
    0x000019c8   0x000019c8   0x00000014   Code   RO         1322    .text.OLED_RS_Set   oled.o
    0x000019dc   0x000019dc   0x0000007c   Code   RO         1318    .text.OLED_Refresh_Gram  oled.o
    0x00001a58   0x00001a58   0x00000010   Code   RO         1326    .text.OLED_SCLK_Clr  oled.o
    0x00001a68   0x00001a68   0x00000010   Code   RO         1332    .text.OLED_SCLK_Set  oled.o
    0x00001a78   0x00001a78   0x00000010   Code   RO         1330    .text.OLED_SDIN_Clr  oled.o
    0x00001a88   0x00001a88   0x00000014   Code   RO         1328    .text.OLED_SDIN_Set  oled.o
    0x00001a9c   0x00001a9c   0x00000114   Code   RO         1342    .text.OLED_ShowChar  oled.o
    0x00001bb0   0x00001bb0   0x0000007a   Code   RO         1348    .text.OLED_ShowString  oled.o
    0x00001c2a   0x00001c2a   0x0000006e   Code   RO         1320    .text.OLED_WR_Byte  oled.o
    0x00001c98   0x00001c98   0x00000068   Code   RO           27    .text.SYSCFG_DL_ADC12_VOLTAGE_init  ti_msp_dl_config.o
    0x00001d00   0x00001d00   0x00000018   Code   RO          121    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x00001d18   0x00001d18   0x00000008   Code   RO           29    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x00001d20   0x00001d20   0x0000012c   Code   RO           15    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001e4c   0x00001e4c   0x0000007c   Code   RO           19    .text.SYSCFG_DL_PWM_0_init  ti_msp_dl_config.o
    0x00001ec8   0x00001ec8   0x00000048   Code   RO           17    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001f10   0x00001f10   0x00000010   Code   RO           31    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001f20   0x00001f20   0x00000030   Code   RO           21    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00001f50   0x00001f50   0x00000058   Code   RO           23    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001fa8   0x00001fa8   0x0000005c   Code   RO           25    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00002004   0x00002004   0x00000038   Code   RO           11    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x0000203c   0x0000203c   0x00000088   Code   RO           13    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x000020c4   0x000020c4   0x0000000c   Code   RO          177    .text.SysTick_Handler  board.o
    0x000020d0   0x000020d0   0x0000000c   Code   RO          189    .text.Systick_getTick  board.o
    0x000020dc   0x000020dc   0x0000000c   Code   RO          179    .text.TI_GetTick    board.o
    0x000020e8   0x000020e8   0x000000c4   Code   RO         1621    .text.Track_Judge   track.o
    0x000021ac   0x000021ac   0x0000007c   Code   RO         1617    .text.Track_Read    track.o
    0x00002228   0x00002228   0x0000005c   Code   RO         1562    .text.UART1_IRQHandler  uart_callback.o
    0x00002284   0x00002284   0x0000002c   Code   RO          150    .text.__NVIC_ClearPendingIRQ  empty.o
    0x000022b0   0x000022b0   0x0000002c   Code   RO          152    .text.__NVIC_EnableIRQ  empty.o
    0x000022dc   0x000022dc   0x00000084   Code   RO           83    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00002360   0x00002360   0x0000000a   Code   RO         1560    .text.bt_control    uart_callback.o
    0x0000236a   0x0000236a   0x00000002   PAD
    0x0000236c   0x0000236c   0x00000030   Code   RO          191    .text.delay_ms      board.o
    0x0000239c   0x0000239c   0x00000090   Code   RO          193    .text.delay_us      board.o
    0x0000242c   0x0000242c   0x00000062   Code   RO          148    .text.main          empty.o
    0x0000248e   0x0000248e   0x00000002   PAD
    0x00002490   0x00002490   0x00000008   Code   RO         1527    .text.scheduler_init  scheduler.o
    0x00002498   0x00002498   0x0000007c   Code   RO         1533    .text.scheduler_run  scheduler.o
    0x00002514   0x00002514   0x00000004   PAD
    0x00002518   0x00002518   0x0000000e   Code   RO         1743    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00002526   0x00002526   0x00000002   PAD
    0x00002528   0x00002528   0x00000002   Code   RO         1744    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000252a   0x0000252a   0x00000006   PAD
    0x00002530   0x00002530   0x0000000e   Code   RO         1745    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000253e   0x0000253e   0x00000002   PAD
    0x00002540   0x00002540   0x00000008   Data   RO          139    .rodata.gADC12_VOLTAGEClockConfig  ti_msp_dl_config.o
    0x00002548   0x00002548   0x00000018   Data   RO          140    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x00002560   0x00002560   0x00000003   Data   RO          131    .rodata.gPWM_0ClockConfig  ti_msp_dl_config.o
    0x00002563   0x00002563   0x00000001   PAD
    0x00002564   0x00002564   0x00000008   Data   RO          132    .rodata.gPWM_0Config  ti_msp_dl_config.o
    0x0000256c   0x0000256c   0x00000028   Data   RO          130    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00002594   0x00002594   0x00000003   Data   RO          133    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00002597   0x00002597   0x00000001   PAD
    0x00002598   0x00002598   0x00000014   Data   RO          134    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x000025ac   0x000025ac   0x00000002   Data   RO          135    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000025ae   0x000025ae   0x0000000a   Data   RO          136    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000025b8   0x000025b8   0x00000002   Data   RO          137    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x000025ba   0x000025ba   0x0000000a   Data   RO          138    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x000025c4   0x000025c4   0x00000474   Data   RO         1363    .rodata.oled_asc2_1206  oled.o
    0x00002a38   0x00002a38   0x000005f0   Data   RO         1364    .rodata.oled_asc2_1608  oled.o
    0x00003028   0x00003028   0x00000005   Data   RO         1586    .rodata.str1.1      initial.o
    0x0000302d   0x0000302d   0x00000003   PAD
    0x00003030   0x00003030   0x00000020   Data   RO         1742    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00003050, Size: 0x000015f0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00003050   0x00000030   Data   RW         1536    .data.scheduler_task  scheduler.o
    0x20200030        -       0x00000002   Zero   RW         1479    .bss.AccX_offset    mpu6050.o
    0x20200032        -       0x00000002   Zero   RW         1480    .bss.AccY_offset    mpu6050.o
    0x20200034        -       0x00000002   Zero   RW         1481    .bss.AccZ_offset    mpu6050.o
    0x20200036        -       0x00000001   Zero   RW         1574    .bss.BTBufferHandler.handleSize  uart_callback.o
    0x20200037        -       0x00000001   Zero   RW         1573    .bss.BTBufferHandler.handleflag  uart_callback.o
    0x20200038        -       0x00000001   Zero   RW         1575    .bss.BTBufferHandler.lastSize  uart_callback.o
    0x20200039   0x00003080   0x00000003   PAD
    0x2020003c        -       0x00000004   Zero   RW         1572    .bss.BTBufferHandler.tick  uart_callback.o
    0x20200040        -       0x00000004   Zero   RW         1434    .bss.Get_Encoder_countA  encoder.o
    0x20200044        -       0x00000004   Zero   RW         1435    .bss.Get_Encoder_countB  encoder.o
    0x20200048        -       0x00000002   Zero   RW         1482    .bss.GyroX_offset   mpu6050.o
    0x2020004a        -       0x00000002   Zero   RW         1483    .bss.GyroY_offset   mpu6050.o
    0x2020004c        -       0x00000002   Zero   RW         1484    .bss.GyroZ_offset   mpu6050.o
    0x2020004e        -       0x00000001   Zero   RW         1632    .bss.L1             track.o
    0x2020004f        -       0x00000001   Zero   RW         1631    .bss.L2             track.o
    0x20200050        -       0x00000400   Zero   RW         1362    .bss.OLED_GRAM      oled.o
    0x20200450        -       0x00000001   Zero   RW         1633    .bss.R1             track.o
    0x20200451        -       0x00000001   Zero   RW         1634    .bss.R2             track.o
    0x20200452        -       0x00000001   Zero   RW         1635    .bss.R3             track.o
    0x20200453        -       0x00000001   Zero   RW         1568    .bss.UART1Counts    uart_callback.o
    0x20200454        -       0x000000c8   Zero   RW         1571    .bss.UART1Packet    uart_callback.o
    0x2020051c        -       0x000000bc   Zero   RW          129    .bss.gPWM_0Backup   ti_msp_dl_config.o
    0x202005d8        -       0x00000004   Zero   RW          199    .bss.g_msTicks      board.o
    0x202005dc        -       0x00000004   Zero   RW         1432    .bss.gpio_interrup1  encoder.o
    0x202005e0        -       0x00000004   Zero   RW         1433    .bss.gpio_interrup2  encoder.o
    0x202005e4        -       0x00000001   Zero   RW         1535    .bss.task_num       scheduler.o
    0x202005e5   0x00003080   0x00000001   PAD
    0x202005e6        -       0x00000002   Zero   RW         1630    .bss.track_state_all_black  track.o
    0x202005e8        -       0x00000002   Zero   RW         1627    .bss.track_state_black  track.o
    0x202005ea        -       0x00000002   Zero   RW         1628    .bss.track_state_white  track.o
    0x202005ec   0x00003080   0x00000004   PAD
    0x202005f0        -       0x00001000   Zero   RW            1    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       228         24          0          0          4       4779   board.o
       112          8          0          0          0       4038   dl_adc12.o
        20          0          0          0          0        583   dl_common.o
       250          4          0          0          0       4151   dl_dma.o
      1214        180          0          0          0      20620   dl_timer.o
       170          8          0          0          0      10630   dl_uart.o
       186          8          0          0          0       2829   empty.o
       358         28          0          0         16       4097   encoder.o
        48          4          5          0          0        625   initial.o
       892         28          0          0         12       4334   mpu6050.o
       484         12          0          0          0       5037   myi2c.o
      1268         20       2660          0       1024       7328   oled.o
       132          8          0         48          1       1621   scheduler.o
        20          4        192          0       4096        616   startup_mspm0g350x_uvision.o
      2454        248        130          0        188      38033   ti_msp_dl_config.o
       342         36          0          0         11       4678   track.o
       660         60          0          0        208       6307   uart_callback.o

    ----------------------------------------------------------------------
      8860        <USER>       <GROUP>         48       5568     120306   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          4          5          0          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       232         20          0          0          0      18490   dl_sysctl_mspm0g1x0x_g3x0x.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        80          6          0          0          0         72   idiv_div0.o
        48         10          0          0          0         68   init.o
        62          0          0          0          0         72   uidiv_div0.o

    ----------------------------------------------------------------------
       484         <USER>          <GROUP>          0          0      18702   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       232         20          0          0          0      18490   driverlib.a
       240         24          0          0          0        212   mc_p.l

    ----------------------------------------------------------------------
       484         <USER>          <GROUP>          0          0      18702   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9344        728       3024         48       5568     138796   Grand Totals
      9344        728       3024         48       5568     138796   ELF Image Totals
      9344        728       3024         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                12368 (  12.08kB)
    Total RW  Size (RW Data + ZI Data)              5616 (   5.48kB)
    Total ROM Size (Code + RO Data + RW Data)      12416 (  12.12kB)

==============================================================================

