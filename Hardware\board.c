#include "ti_msp_dl_config.h"
#include "board.h"

volatile unsigned long tick_ms;
volatile uint32_t start_time;
volatile uint32_t g_msTicks = 0; // 系统毫秒计数器

// 魔改HAL_GetTick函数
void SysTick_Handler(void)
{
	g_msTicks++; // 每1ms中断一次
}
uint32_t TI_GetTick(void)
{
	return g_msTicks;
}

// printf函数重定义
int fputc(int ch, FILE *stream)
{
	// 当串口0忙的时候等待，不忙的时候再发送传进来的字符
	while (DL_UART_isBusy(UART_1_INST) == true)
		;

	DL_UART_Main_transmitDataBlocking(UART_1_INST, ch);

	return ch;
}

int fputs(const char *restrict s, FILE *restrict stream)
{
	uint16_t i, len;
	len = strlen(s);
	for (i = 0; i < len; i++)
	{
		DL_UART_Main_transmitDataBlocking(UART_1_INST, s[i]);
	}
	return len;
}

int puts(const char *_ptr)
{
	int count = fputs(_ptr, stdout);
	count += fputs("\n", stdout);
	return count;
}

// 返回SysTick计数值
uint32_t Systick_getTick(void)
{
	return (SysTick->VAL);
}

// ms阻塞延迟
void delay_ms(uint32_t ms)
{
	// 超出能满足的最大延迟
	// if( ms > SysTickMAX_COUNT/(SysTickFre/1000) ) ms = SysTickMAX_COUNT/(SysTickFre/1000);
	for (int i = 0; i < 1000; i++)
	{
		delay_us(ms);
	}
}

void delay_us(uint32_t us)
{
	if (us > SysTickMAX_COUNT / (SysTickFre / 1000000))
		us = SysTickMAX_COUNT / (SysTickFre / 1000000);

	us = us * (SysTickFre / 1000000); // 单位转换

	// 用于保存已走过的时间
	uint32_t runningtime = 0;

	// 获得当前时刻的计数值
	uint32_t InserTick = Systick_getTick();

	// 用于刷新实时时间
	uint32_t tick = 0;

	uint8_t countflag = 0;
	// 等待延迟
	while (1)
	{
		tick = Systick_getTick(); // 刷新当前时刻计数值

		if (tick > InserTick)
			countflag = 1; // 出现溢出轮询,则切换走时的计算方式

		if (countflag)
			runningtime = InserTick + SysTickMAX_COUNT - tick;
		else
			runningtime = InserTick - tick;

		if (runningtime >= us)
			break;
	}
}

void delay_1us(unsigned long __us) { delay_us(__us); }
void delay_1ms(unsigned long ms) { delay_ms(ms); }
