{"name": "empty_LP_MSPM0G3507_nortos_keil", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Source", "files": [{"path": "../empty.syscfg"}, {"path": "../keil/startup_mspm0g350x_uvision.s"}, {"path": "../ti_msp_dl_config.h"}, {"path": "../ti_msp_dl_config.c"}, {"path": "../empty.c"}, {"path": "../Hardware/board.c"}], "folders": []}, {"name": "Driver<PERSON><PERSON>", "files": [{"path": "../ti/dl_vref.c"}, {"path": "../ti/dl_uart.c"}, {"path": "../ti/dl_trng.c"}, {"path": "../ti/dl_timer.c"}, {"path": "../ti/dl_spi.c"}, {"path": "../ti/dl_rtc_common.c"}, {"path": "../ti/dl_opa.c"}, {"path": "../ti/dl_mcan.c"}, {"path": "../ti/dl_mathacl.c"}, {"path": "../ti/dl_lfss.c"}, {"path": "../ti/dl_lcd.c"}, {"path": "../ti/dl_keystorectl.c"}, {"path": "../ti/dl_i2c.c"}, {"path": "../ti/dl_flashctl.c"}, {"path": "../ti/dl_dma.c"}, {"path": "../ti/dl_dac12.c"}, {"path": "../ti/dl_crcp.c"}, {"path": "../ti/dl_crc.c"}, {"path": "../ti/dl_common.c"}, {"path": "../ti/dl_aesadv.c"}, {"path": "../ti/dl_aes.c"}, {"path": "../ti/dl_adc12.c"}, {"path": "../source/ti/driverlib/m0p/dl_interrupt.c"}, {"path": "../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a"}], "folders": []}, {"name": "Hardware", "files": [{"path": "../Hardware/oled.c"}, {"path": "../Hardware/key.c"}, {"path": "../Hardware/led.c"}, {"path": "../Hardware/motor.c"}, {"path": "../Hardware/encoder.c"}], "folders": []}, {"name": "Control", "files": [{"path": "../Control/DataScope_DP.C"}], "folders": []}, {"name": "code", "files": [{"path": "../code/MPU6050.c"}, {"path": "../code/MPU6050.h"}, {"path": "../code/MPU6050_Reg.h"}, {"path": "../code/MyI2C.c"}, {"path": "../code/MyI2C.h"}, {"path": "../code/scheduler.c"}, {"path": "../code/scheduler.h"}, {"path": "../code/uart_callback.c"}, {"path": "../code/uart_callback.h"}, {"path": "../code/Initial.c"}, {"path": "../code/Initial.h"}, {"path": "../code/track.c"}, {"path": "../code/track.h"}, {"path": "../code/PID.h"}, {"path": "../code/PID.c"}, {"path": "../code/interrupt.h"}, {"path": "../code/interrupt.c"}], "folders": []}]}, "outDir": "build", "deviceName": "MSPM0G3507", "packDir": ".pack/TexasInstruments/MSPM0G1X0X_G3X0X_DFP.1.3.1", "miscInfo": {"uid": "290270ac8f07e0984cbac44c0ccc2988"}, "targets": {"MSPM0G3507_Project": {"excludeList": ["<virtual_root>/Control/show.c", "<virtual_root>/Hardware/adc.c", "<virtual_root>/Hardware/IR_Module.c", "<virtual_root>/Control/control.c", "<virtual_root>/Control/uart_callback.c"], "toolchain": "AC6", "compileConfig": {"cpuType": "Cortex-M0+", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "../keil/mspm0g3507.sct", "useCustomScatterFile": true, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x8000"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x20200000", "size": "0x8000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x20000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x400000", "size": "0x20000"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "ti_mspm0", "interface": "cmsis-dap", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "../source", "../source/third_party/CMSIS/Core/Include", "../Hardware", "..", "../Control", ".cmsis/include", "../keil/RTE/_MSPM0G3507_Project", "../code"], "libList": [], "defineList": ["MPU6050", "MOTION_DRIVER_TARGET_MSPM0", "__MSPM0G3507__"]}, "builderOptions": {"AC6": {"version": 3, "beforeBuildTasks": [{"name": "cmd.exe /C \"$P../tools/keil/syscfg.bat '$P' empty.syscfg\"", "command": "cd .\\..\\keil && cmd.exe /C \"..\\tools\\keil\\syscfg.bat ..\\ empty.syscfg\"", "disable": false, "abortAfterFailed": true, "stopBuildAfterFailed": true}], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "language-c": "c99", "language-cpp": "c++11", "one-elf-section-per-function": true, "short-enums#wchar": true, "warnings": "ac5-like-warnings"}, "asm-compiler": {"$use": "asm-auto"}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "misc-controls": "../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a", "ro-base": "0x00000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}