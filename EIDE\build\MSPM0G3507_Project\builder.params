{"name": "empty_LP_MSPM0G3507_nortos_keil", "target": "MSPM0G3507_Project", "toolchain": "AC6", "toolchainLocation": "D:\\Keil_v5\\ARM\\ARMCLANG", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\data\\models/arm.v6.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 12, "rootDir": "c:\\Users\\<USER>\\Desktop\\ds\\TI_2025\\TI_2025\\TI_2025\\EIDE", "dumpPath": "build\\MSPM0G3507_Project", "outDir": "build\\MSPM0G3507_Project", "incDirs": [".", "../source", "../source/third_party/CMSIS/Core/Include", "../Hardware", "..", "../Control", ".cmsis/include", "../keil/RTE/_MSPM0G3507_Project", "../code"], "libDirs": [], "defines": ["MPU6050", "MOTION_DRIVER_TARGET_MSPM0", "__MSPM0G3507__"], "sourceList": ["../Control/DataScope_DP.C", "../Hardware/board.c", "../Hardware/encoder.c", "../Hardware/key.c", "../Hardware/led.c", "../Hardware/motor.c", "../Hardware/oled.c", "../code/Initial.c", "../code/MPU6050.c", "../code/MyI2C.c", "../code/PID.c", "../code/interrupt.c", "../code/scheduler.c", "../code/track.c", "../code/uart_callback.c", "../empty.c", "../keil/startup_mspm0g350x_uvision.s", "../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a", "../source/ti/driverlib/m0p/dl_interrupt.c", "../ti/dl_adc12.c", "../ti/dl_aes.c", "../ti/dl_aesadv.c", "../ti/dl_common.c", "../ti/dl_crc.c", "../ti/dl_crcp.c", "../ti/dl_dac12.c", "../ti/dl_dma.c", "../ti/dl_flashctl.c", "../ti/dl_i2c.c", "../ti/dl_keystorectl.c", "../ti/dl_lcd.c", "../ti/dl_lfss.c", "../ti/dl_mathacl.c", "../ti/dl_mcan.c", "../ti/dl_opa.c", "../ti/dl_rtc_common.c", "../ti/dl_spi.c", "../ti/dl_timer.c", "../ti/dl_trng.c", "../ti/dl_uart.c", "../ti/dl_vref.c", "../ti_msp_dl_config.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 3, "beforeBuildTasks": [{"name": "cmd.exe /C \"$P../tools/keil/syscfg.bat '$P' empty.syscfg\"", "command": "cd .\\..\\keil && cmd.exe /C \"..\\tools\\keil\\syscfg.bat ..\\ empty.syscfg\"", "disable": false, "abortAfterFailed": true, "stopBuildAfterFailed": true}], "afterBuildTasks": [{"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m0+", "microcontroller-fpu": "cortex-m0+", "microcontroller-float": "cortex-m0+", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "language-c": "c99", "language-cpp": "c++11", "one-elf-section-per-function": true, "short-enums#wchar": true, "warnings": "ac5-like-warnings"}, "asm-compiler": {"$use": "asm-auto"}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "misc-controls": "../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a", "ro-base": "0x00000000", "rw-base": "0x20000000", "link-scatter": ["c:/Users/<USER>/Desktop/ds/TI_2025/TI_2025/TI_2025/keil/mspm0g3507.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "Objects", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\ds\\TI_2025\\TI_2025\\TI_2025\\EIDE", "workspaceFolderBasename": "EIDE", "OutDir": "c:\\Users\\<USER>\\Desktop\\ds\\TI_2025\\TI_2025\\TI_2025\\EIDE\\build\\MSPM0G3507_Project", "OutDirRoot": "build", "OutDirBase": "build\\MSPM0G3507_Project", "ProjectName": "empty_LP_MSPM0G3507_nortos_keil", "ConfigName": "MSPM0G3507_Project", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\ds\\TI_2025\\TI_2025\\TI_2025\\EIDE", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\ds\\TI_2025\\TI_2025\\TI_2025\\EIDE\\build\\MSPM0G3507_Project\\empty_LP_MSPM0G3507_nortos_keil", "ChipPackDir": ".pack/TexasInstruments/MSPM0G1X0X_G3X0X_DFP.1.3.1", "ChipName": "MSPM0G3507", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "D:\\Keil_v5\\ARM\\ARMCLANG"}, "sysPaths": []}