#include "track.h"
#include "board.h"

uint8_t L1, L2, R1, R2, R3;     // 5路循迹传感器
uint16_t track_state_black = 0; // 灰度传感器状态
uint16_t track_state_white = 0; // 灰度传感器状态
uint8_t track_state = 0;        //
uint16_t track_state_all_black = 0;
//按需修改
#define L2_Port GPIOA
#define L2_Pin DL_GPIO_PIN_1
#define L1_Port GPIOA
#define L1_Pin DL_GPIO_PIN_6
#define R1_Port GPIOA
#define R1_Pin DL_GPIO_PIN_5
#define R2_Port GPIOA
#define R2_Pin DL_GPIO_PIN_4
#define R3_Port GPIOA
#define R3_Pin DL_GPIO_PIN_3

/*灰度循迹初始化函数*/
/*初始化灰度模块，读取5路灰度传感器的引脚*/
//1白底 0黑线
void Track_Read()
{
    L2 = DL_GPIO_readPins(L2_Port, L2_Pin) ? 1 : 0; // 左外侧
    L1 = DL_GPIO_readPins(L1_Port, L1_Pin) ? 1 : 0; // 左内侧
    R1 = DL_GPIO_readPins(R1_Port, R1_Pin) ? 1 : 0; // 中间
    R2 = DL_GPIO_readPins(R2_Port, R2_Pin) ? 1 : 0; // 右内侧
    R3 = DL_GPIO_readPins(R3_Port, R3_Pin) ? 1 : 0; // 右外侧
}
/*
 * 灰度循迹判断函数
 * 根据灰度传感器的状态，判断当前是否检测到黑线或白底
 * 检测到黑线则累加track_state_black，检测到白底则累加track_state_white
 * 检测周期建议50ms
 * 如果检测到全黑，则track_state_all_black置为1
 */
void Track_Judge()
{
    Track_Read();
    if (L2 == 0 || L1 == 0 || R1 == 0 || R2 == 0 || R3 == 0)
    {
        track_state_black = track_state_black + 1; // 检测到黑线
    }
    else
    {
        track_state_black = 0; // 未检测到黑线
    }
    // 检测到全白底
    if (L2 == 1 && L1 == 1 && R1 == 1 && R2 == 1 && R3 == 1)
    {
        track_state_white = track_state_white + 1; // 检测到白底
    }
    else
    {
        track_state_white = 0;
    }
    if (L1 == 0 && R1 == 0 && R2 == 0)
    {
        track_state_all_black = 1; // 检测到全黑
    }
}

void Track_Process()
{
    static uint8_t last_R3 = 1; // 初始认为未识别到黑线
    static uint8_t last_L2 = 1; // 初始认为未识别到黑线

    // 检查R3上一次检测到黑线，这次未检测到且所有探头未检测到黑线
    if (last_R3 == 0 && L2 == 1 && L1 == 1 && R1 == 1 && R2 == 1 && R3 == 1)
    {
        pid_track.Actual = 8; // 右侧大转弯
    }
    // 检查L2上一次检测到黑线，这次未检测到且所有探头未检测到黑线
    else if (last_L2 == 0 && L2 == 1 && L1 == 1 && R1 == 1 && R2 == 1 && R3 == 1)
    {
        pid_track.Actual = -8; // 左侧大转弯
    }
    else if (L2 == 0)
    {
        pid_track.Actual = -6; // 左外侧偏移
    }
    else if (R3 == 0)
    {
        pid_track.Actual = 6; // 右外侧偏移
    }
    else if (L1 == 0 && L2 == 0)
    {
        pid_track.Actual = -4; // 左侧组合
    }
    else if (R2 == 0 && R3 == 0)
    {
        pid_track.Actual = 4; // 右侧组合
    }
    else if (L1 == 0)
    {
        pid_track.Actual = -2; // 左内侧偏移
    }
    else if (R2 == 0)
    {
        pid_track.Actual = 2; // 右内侧偏移
    }
    else if (R1 == 0)
    {
        pid_track.Actual = 0; // 中心线
    }
    else
    {
        pid_track.Actual = 0; // 默认直行
    }

    last_R3 = R3;
    last_L2 = L2;
}

/*灰度循迹配速函数*/
/*根据Track_Mode的值，分配两个轮子的速度*/
void Track_PID_Update(int16_t base_speed)
{
     PID_Update(&pid_track);
     int16_t left_speed = (base_speed - pid_track.Out);
     int16_t right_speed = (base_speed + pid_track.Out);
     pid_left.Target = left_speed;   // 设置左轮目标速度
     pid_right.Target = right_speed; // 设置右轮目标速度
}
