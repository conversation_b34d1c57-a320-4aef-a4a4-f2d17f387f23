#include "ti_msp_dl_config.h"
#include "board.h"
#include "uart_callback.h"

#define UART1_PACKET_SIZE (200)                // 定义数据包缓冲区大小
volatile uint8_t UART1Packet[UART1_PACKET_SIZE]; // 数据接收缓冲区
volatile uint8_t UART1Counts = 0;             // 当前接收数据计数
volatile uint8_t lastUART1Counts = 0;          // 上次接收数据计数
volatile bool UART1gCheck;                      // 数据接收完成标志

uint8_t RecvOverFlag = 0; // 接收完成标志

void bt_control(uint8_t recv); // 数据处理函数声明

// DMA配置函数：配置DMA通道，实现UART串口数据自动搬运到内存缓冲区
void BT_DAMConfig(void)
{
    DL_DMA_disableChannel(DMA, DMA_CH0_CHAN_ID);                               // 禁用DMA通道
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)(&UART_1_INST->RXDATA)); // 设置DMA源地址为UART接收寄存器
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&UART1Packet[0]);         // 设置DMA目标地址为接收缓冲区
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, UART1_PACKET_SIZE);              // 设置DMA传输数据量
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);                                // 启用DMA通道
}

// 串口数据缓冲区处理函数：管理接收到的数据，处理超时和缓冲区溢出
void BTBufferHandler(void)
{
    static uint32_t tick = 0;
    static uint8_t handleflag = 0; // 数据处理标志
    static uint8_t handleSize = 0; // 已处理数据大小
    static uint8_t lastSize = 0;   // 上次接收数据大小

    // 计算已接收数据量
    uint8_t recvsize = UART1_PACKET_SIZE - DL_DMA_getTransferSize(DMA, DMA_CH0_CHAN_ID);

    if (recvsize != lastSize) // 有新数据到达
    {
        handleflag = 1;           // 标记需要处理
        tick = Systick_getTick(); // 刷新接收时间戳
    }
    else // 无新数据
    {
        // 超时且有待处理数据
        if (((tick - Systick_getTick()) & SysTickMAX_COUNT) >= SysTick_MS(1) && handleflag == 1)
        {
            handleflag = 0;
            // 处理新接收的数据
            for (uint8_t i = handleSize; i < recvsize; i++)
                bt_control(UART1Packet[i]);

            handleSize = recvsize;

            // 缓冲区过半时重置DMA，防止溢出
            if (recvsize >= UART1_PACKET_SIZE / 2)
            {
                recvsize = 0;
                handleSize = 0;
                lastSize = 0;
                BT_DAMConfig();
            }
        }
    }

    lastSize = recvsize;
}

// UART1中断服务函数：处理串口接收和DMA完成中断
void UART_1_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_1_INST))
    {
        case DL_UART_IIDX_RX:                                               // 串口接收中断
            if (UART1Counts < UART1_PACKET_SIZE)
            {
                UART1Packet[UART1Counts++] = DL_UART_Main_receiveData(UART_1_INST); // 读取数据到缓冲区
            }
            else
            {
                UART1Counts = 0; // 或者做其他处理
            }
            break;
        case DL_UART_MAIN_IIDX_DMA_DONE_RX: // DMA接收完成中断
            BT_DAMConfig();                 // 重新配置DMA
        default:
            break;
    }
}
// 串口数据处理函数：用户可在此处实现自定义数据解析和处理逻辑
void bt_control(uint8_t recv)
{
    // 留空，用户可根据实际串口协议实现数据处理
}
