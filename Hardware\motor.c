#include "motor.h"
/***********************************************
公司：轮趣科技（东莞）有限公司
品牌：WHEELTEC
官网：wheeltec.net
淘宝店铺：shop114407458.taobao.com
速卖通: https://minibalance.aliexpress.com/store/4455017
版本：V1.0
修改时间：2024-07-019

Brand: WHEELTEC
Website: wheeltec.net
Taobao shop: shop114407458.taobao.com
Aliexpress: https://minibalance.aliexpress.com/store/4455017
Version: V1.0
Update：2024-07-019

All rights reserved
***********************************************/
void Set_PWM(int pwmL, int pwmR)
{
  if (pwmL > 0)
  {
    DL_GPIO_setPins(AIN_PORT, AIN_AIN2_PIN);
    DL_GPIO_clearPins(AIN_PORT, AIN_AIN1_PIN);
    DL_Timer_setCaptureCompareValue(PWM_0_INST, ABS(pwmL), GPIO_PWM_0_C0_IDX);
  }
  else
  {
    DL_GPIO_setPins(AIN_PORT, AIN_AIN1_PIN);
    DL_GPIO_clearPins(AIN_PORT, AIN_AIN2_PIN);
    DL_Timer_setCaptureCompareValue(PWM_0_INST, ABS(pwmL), GPIO_PWM_0_C0_IDX);
  }
  if (pwmR > 0)
  {
    DL_GPIO_setPins(BIN_PORT, BIN_BIN2_PIN);
    DL_GPIO_clearPins(BIN_PORT, BIN_BIN1_PIN);
    DL_Timer_setCaptureCompareValue(PWM_0_INST, ABS(pwmR), GPIO_PWM_0_C1_IDX);
  }
  else
  {
    DL_GPIO_setPins(BIN_PORT, BIN_BIN1_PIN);
    DL_GPIO_clearPins(BIN_PORT, BIN_BIN2_PIN);
    DL_Timer_setCaptureCompareValue(PWM_0_INST, ABS(pwmR), GPIO_PWM_0_C1_IDX);
  }
}
