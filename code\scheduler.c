#include "scheduler.h"
#include "board.h"

uint8_t task_num;


/*任务调度*/
/*将自己写好的proc函数放在下面调度中运行*/
/*函数名 执行间隔 上次执行时间 是否使能*/
static task_t scheduler_task[] = {
    {BTBufferHandler, 0, 0, 1},   // 蓝牙数据处理任务，while1执行
    {OLED_Refresh_Gram, 100, 0, 1},      // OLED刷新任务，100ms执行一次
    {Track_Judge, 50, 0, 1},      // 灰度循迹处理任务，50ms执行一次
};

void scheduler_init() // 调度初始化函数
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

// 通过函数名开启任务
void scheduler_enable_task(void (*func)(void))
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        if (scheduler_task[i].task_func == func)
        {
            scheduler_task[i].enable = 1;
            break;
        }
    }
}
// 通过函数名关闭任务
void scheduler_disable_task(void (*func)(void))
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        if (scheduler_task[i].task_func == func)
        {
            scheduler_task[i].enable = 0;
            break;
        }
    }
}

void scheduler_run()
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        if (!scheduler_task[i].enable)
            continue; // 跳过未使能任务
        uint32_t now_time = TI_GetTick(); // 获取当前时间
        if (now_time >= (scheduler_task[i].rate_ms + scheduler_task[i].last_run))
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}
