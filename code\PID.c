#include "PID.h"
#include "board.h"

// 左轮PID控制器
PID_t pid_left = {
    .Target = 0,            // 目标值
    .Actual = 0,            // 实际值
    .Actual1 = 0,           // 上次实际值
    .Out = 0,               // 输出值
    .Err0 = 0,              // 当前误差
    .Err1 = 0,              // 上次误差
    .ErrInt = 0,            // 积分值
    .ErrIntThreshold = 50, // 积分阈值
    .ErrIntLimit = 0,    // 积分限幅
    .Kp = 20,             // 比例系数
    .Ki = 0.3,             // 积分系数
    .Kd = 0,                // 微分系数
    .OutMax = 1000,         // 输出最大值
    .OutMin = -1000,        // 输出最小值
}; // 左轮PID控制器

// 右轮PID控制器
PID_t pid_right = {
    .Target = 0,            // 目标值
    .Actual = 0,            // 实际值
    .Actual1 = 0,           // 上次实际值
    .Out = 0,               // 输出值
    .Err0 = 0,              // 当前误差
    .Err1 = 0,              // 上次误差
    .ErrInt = 0,            // 积分值
    .ErrIntThreshold = 50, // 积分阈值
    .ErrIntLimit = 0,    // 积分限幅
    .Kp = 16,             // 比例系数
    .Ki = 0.5,              // 积分系数
    .Kd = 0,                // 微分系数
    .OutMax = 1000,         // 输出最大值
    .OutMin = -1000,        // 输出最小值
}; // 右轮PID控制器

// 灰度循迹PID控制器
PID_t pid_track = {
    .Target = 0,            // 目标值
    .Actual = 0,            // 实际值
    .Actual1 = 0,           // 上次实际值
    .Out = 0,               // 输出值
    .Err0 = 0,              // 当前误差
    .Err1 = 0,              // 上次误差
    .ErrInt = 0,            // 积分值
    .ErrIntThreshold = 100, // 积分阈值
    .Kp = 5.3,              // 比例系数
    .Ki = 0,                // 积分系数
    .Kd = 0,                // 微分系数
    .OutMax = 1000,         // 输出最大值
    .OutMin = -1000,        // 输出最小值
}; // 灰度循迹PID控制器

// 外环PID类型枚举
OuterPIDType_t pid_type = PID_STOP; // 默认为停车

// 编码器累计值在encoder.c文件里

int16_t T_base_speed = 80; // 灰度循迹环基准速度
int16_t A_base_speed = 80; // 角度直行环基准速度

void PID_Clear(PID_t *p)
{
    p->Err0 = 0;
    p->Err1 = 0;
    p->ErrInt = 0;
}

/*
PID函数
 * 根据pid_type选择外环PID类型
 * PID_TRACK 灰度循迹环
 * PID_STOP  停车
 * PID_SPEED 关闭外环 仅速度环

 * T_base_speed为灰度循迹环基准速度，A_base_speed为角度直行环基准速度
 * 变量均为全局变量
 */

void PID(void)
{
    // 选择外环PID类型
    switch (pid_type)
    {
    case PID_TRACK: // 灰度循迹环
        Track_Read();
        Track_Process();
        Track_PID_Update(T_base_speed);
        break;
    case PID_STOP: // 停车
        pid_left.Target = 0;
        pid_right.Target = 0;
        break;
    case PID_SPEED: // 仅速度环
        // 在速度环模式下，目标速度在main函数中设置
        // 这里不做任何操作，保持目标速度不变
        break;
    default:
        pid_right.Target = 0;
        pid_left.Target = 0;
        // 默认停下
        break;
    }
   //内环速度环
   PID_Speed_Update();
}

void PID_Update_weifenxianxing(PID_t *p)
{
    // 将值更新
    p->Err1 = p->Err0;
    p->Err0 = p->Target - p->Actual;
    p->Actual1 = p->Actual;
    // 积分分离（只有ErrIntThreshold非0时启用）
    if (p->Ki != 0)
    {
        if (p->ErrIntThreshold == 0)
        {
            p->ErrInt += p->Err0;
        }
        else if (fabs(p->Err0) < p->ErrIntThreshold)
        {
            p->ErrInt += p->Err0;
        }
        else
        {
            p->ErrInt = 0;
        }
    }
    else
    {
        p->ErrInt = 0;
    }
    // 积分限幅（只有ErrIntLimit非0时启用）
    if (p->ErrIntLimit != 0)
    {
        if (p->ErrInt > p->ErrIntLimit)
        {
            p->ErrInt = p->ErrIntLimit;
        }
        if (p->ErrInt < -p->ErrIntLimit)
        {
            p->ErrInt = -p->ErrIntLimit;
        }
    }
    // PID计算
    p->Out = p->Kp * p->Err0 + p->Ki * p->ErrInt - p->Kd * (p->Actual - p->Actual1);
    // 输出限幅
    if (p->Out > p->OutMax)
    {
        p->Out = p->OutMax;
    }
    if (p->Out < p->OutMin)
    {
        p->Out = p->OutMin;
    }
}

void PID_Update(PID_t *p)
{
    // 将值更新
    p->Err1 = p->Err0;
    p->Err0 = p->Target - p->Actual;
    p->Actual1 = p->Actual;
    // 积分分离（只有ErrIntThreshold非0时启用）
    if (p->Ki != 0)
    {
        if (p->ErrIntThreshold == 0)
        {
            p->ErrInt += p->Err0;
        }
        else if (fabs(p->Err0) < p->ErrIntThreshold)
        {
            p->ErrInt += p->Err0;
        }
        else
        {
            p->ErrInt = 0;
        }
    }
    else
    {
        p->ErrInt = 0;
    }
    // 积分限幅（只有ErrIntLimit非0时启用）
    if (p->ErrIntLimit != 0)
    {
        if (p->ErrInt > p->ErrIntLimit)
        {
            p->ErrInt = p->ErrIntLimit;
        }
        if (p->ErrInt < -p->ErrIntLimit)
        {
            p->ErrInt = -p->ErrIntLimit;
        }
    }
    // PID计算
    p->Out = p->Kp * p->Err0 + p->Ki * p->ErrInt + p->Kd * (p->Err0 - p->Err1);
    // 输出限幅
    if (p->Out > p->OutMax)
    {
        p->Out = p->OutMax;
    }
    if (p->Out < p->OutMin)
    {
        p->Out = p->OutMin;
    }
}

void PID_Speed_Update(void) // PID速度更新 //满速PWM对应78，取60满速
{
    // 获取编码器计数（来自encoder.c）
    int left_count = Get_Encoder_countA;
    int right_count = Get_Encoder_countB;

    // 更新PID实际值为速度
    pid_left.Actual = left_count;
    pid_right.Actual = right_count;

    // PID计算
    PID_Update_weifenxianxing(&pid_left);
    PID_Update_weifenxianxing(&pid_right);

    // 设置电机PWM（motor.c）
    Set_PWM((int)pid_left.Out, (int)pid_right.Out);

    //清零复位速度获取
    Get_Encoder_countA = 0;
    Get_Encoder_countB = 0;
}
