/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.1+4189"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const GPIO9   = GPIO.addInstance();
const GPIO10  = GPIO.addInstance();
const GPIO11  = GPIO.addInstance();
const GPIO12  = GPIO.addInstance();
const GPIO13  = GPIO.addInstance();
const GPIO14  = GPIO.addInstance();
const GPIO15  = GPIO.addInstance();
const GPIO16  = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const TIMER2  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                         = "OLED_RST";
GPIO1.associatedPins[0].$name       = "PIN_RST";
GPIO1.associatedPins[0].pin.$assign = "PB14";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "OLED_DC";
GPIO2.associatedPins[0].$name       = "PIN_DC";
GPIO2.associatedPins[0].pin.$assign = "PB15";

GPIO3.$name                         = "OLED_SCL";
GPIO3.associatedPins[0].$name       = "PIN_SCL";
GPIO3.associatedPins[0].pin.$assign = "PA28";

GPIO4.$name                         = "OLED_SDA";
GPIO4.associatedPins[0].$name       = "PIN_SDA";
GPIO4.associatedPins[0].pin.$assign = "PA31";

GPIO5.$name                         = "KEY";
GPIO5.associatedPins[0].$name       = "key";
GPIO5.associatedPins[0].direction   = "INPUT";
GPIO5.associatedPins[0].pin.$assign = "PA18";

GPIO6.$name                         = "LED";
GPIO6.associatedPins[0].$name       = "led";
GPIO6.associatedPins[0].pin.$assign = "PB9";

GPIO7.$name                         = "AIN";
GPIO7.port                          = "PORTA";
GPIO7.associatedPins.create(2);
GPIO7.associatedPins[0].$name       = "AIN1";
GPIO7.associatedPins[0].pin.$assign = "PA16";
GPIO7.associatedPins[1].$name       = "AIN2";
GPIO7.associatedPins[1].pin.$assign = "PA17";

GPIO8.$name                         = "BIN";
GPIO8.port                          = "PORTA";
GPIO8.associatedPins.create(2);
GPIO8.associatedPins[0].$name       = "BIN1";
GPIO8.associatedPins[0].pin.$assign = "PA14";
GPIO8.associatedPins[1].$name       = "BIN2";
GPIO8.associatedPins[1].pin.$assign = "PA13";

GPIO9.$name                               = "ENCODERA";
GPIO9.associatedPins.create(2);
GPIO9.associatedPins[0].$name             = "E1A";
GPIO9.associatedPins[0].direction         = "INPUT";
GPIO9.associatedPins[0].polarity          = "RISE";
GPIO9.associatedPins[0].interruptPriority = "0";
GPIO9.associatedPins[0].interruptEn       = true;
GPIO9.associatedPins[0].pin.$assign       = "PA25";
GPIO9.associatedPins[1].$name             = "E1B";
GPIO9.associatedPins[1].direction         = "INPUT";
GPIO9.associatedPins[1].polarity          = "RISE";
GPIO9.associatedPins[1].interruptPriority = "0";
GPIO9.associatedPins[1].interruptEn       = true;
GPIO9.associatedPins[1].pin.$assign       = "PA26";

GPIO10.$name                               = "ENCODERB";
GPIO10.associatedPins.create(2);
GPIO10.associatedPins[0].$name             = "E2A";
GPIO10.associatedPins[0].direction         = "INPUT";
GPIO10.associatedPins[0].polarity          = "RISE";
GPIO10.associatedPins[0].interruptPriority = "0";
GPIO10.associatedPins[0].interruptEn       = true;
GPIO10.associatedPins[0].pin.$assign       = "PB20";
GPIO10.associatedPins[1].$name             = "E2B";
GPIO10.associatedPins[1].direction         = "INPUT";
GPIO10.associatedPins[1].polarity          = "RISE";
GPIO10.associatedPins[1].interruptPriority = "0";
GPIO10.associatedPins[1].interruptEn       = true;
GPIO10.associatedPins[1].pin.$assign       = "PB24";

GPIO11.$name                         = "IR_DH4";
GPIO11.associatedPins[0].$name       = "PIN_17";
GPIO11.associatedPins[0].direction   = "INPUT";
GPIO11.associatedPins[0].pin.$assign = "PB17";

GPIO12.$name                         = "IR_DH3";
GPIO12.associatedPins[0].$name       = "PIN_16";
GPIO12.associatedPins[0].direction   = "INPUT";
GPIO12.associatedPins[0].pin.$assign = "PB16";

GPIO13.$name                         = "IR_DH2";
GPIO13.associatedPins[0].$name       = "PIN_12";
GPIO13.associatedPins[0].direction   = "INPUT";
GPIO13.associatedPins[0].pin.$assign = "PA12";

GPIO14.$name                         = "IR_DH1";
GPIO14.associatedPins[0].$name       = "PIN_27";
GPIO14.associatedPins[0].direction   = "INPUT";
GPIO14.associatedPins[0].pin.$assign = "PA27";

GPIO15.$name                         = "MPU6050_SCL";
GPIO15.associatedPins[0].$name       = "PIN_0";
GPIO15.associatedPins[0].pin.$assign = "PA1";

GPIO16.$name                         = "MPU6050_SDA";
GPIO16.associatedPins[0].$name       = "PIN_1";
GPIO16.associatedPins[0].pin.$assign = "PA0";

PWM1.$name                      = "PWM_0";
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.timerCount                 = 8000;
PWM1.peripheral.$assign         = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign = "PB2";
PWM1.peripheral.ccp1Pin.$assign = "PB3";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable  = true;
SYSTICK.systickEnable = true;
SYSTICK.period        = 16777216;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkPrescale   = 32;
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "5 ms";
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.peripheral.$assign = "TIMG0";

TIMER2.$name             = "TIMER_Interrupt";
TIMER2.timerMode         = "PERIODIC_UP";
TIMER2.timerPeriod       = "1 ms";
TIMER2.timerClkPrescale  = 2;
TIMER2.timerStartTimer   = true;
TIMER2.interrupts        = ["ZERO"];
TIMER2.interruptPriority = "1";

UART1.$name                    = "UART_0";
UART1.profile                  = "CONFIG_PROFILE_1";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

UART2.$name                      = "UART_1";
UART2.enabledInterrupts          = ["DMA_DONE_RX"];
UART2.interruptPriority          = "1";
UART2.enabledDMARXTriggers       = "DL_UART_DMA_INTERRUPT_RX";
UART2.peripheral.$assign         = "UART1";
UART2.peripheral.rxPin.$assign   = "PB7";
UART2.peripheral.txPin.$assign   = "PB6";
UART2.txPinConfig.$name          = "ti_driverlib_gpio_GPIOPinGeneric6";
UART2.rxPinConfig.$name          = "ti_driverlib_gpio_GPIOPinGeneric7";
UART2.DMA_CHANNEL_RX.$name       = "DMA_CH0";
UART2.DMA_CHANNEL_RX.addressMode = "f2b";
UART2.DMA_CHANNEL_RX.srcLength   = "BYTE";
UART2.DMA_CHANNEL_RX.dstLength   = "BYTE";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution                = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution       = "PA20";
Board.peripheral.swdioPin.$suggestSolution       = "PA19";
TIMER2.peripheral.$suggestSolution               = "TIMA0";
UART1.peripheral.$suggestSolution                = "UART0";
UART2.DMA_CHANNEL_RX.peripheral.$suggestSolution = "DMA_CH0";
