#include "stdint.h"
#include "ti_msp_dl_config.h"

#ifndef __MPU6050_H
#define __MPU6050_H
// 仅使用void MPU6050_ZiTaiJieSuan(void);可有姿态角变量为float Z_Angle

void MPU6050_WriteReg(uint8_t RegAddress, uint8_t Data);
uint8_t MPU6050_ReadReg(uint8_t RegAddress);
void MPU6050_Init(void);
uint8_t MPU6050_GetID(void);
void MPU6050_GetData(int16_t *AccX, int16_t *AccY, int16_t *AccZ,
                     int16_t *GyroX, int16_t *GyroY, int16_t *GyroZ);
void MPU6050_GetRealData(float *AccX, float *AccY, float *AccZ,
                         float *GyroX, float *GyroY, float *GyroZ);
void MPU6050_GetCalibratedData(int16_t *AccX, int16_t *AccY, int16_t *AccZ,
                               int16_t *GyroX, int16_t *GyroY, int16_t *GyroZ);
void MPU6050_CalculateZAngle(float gyroZ);
void MPU6050_ZiTaiJieSuan(void);

#endif
