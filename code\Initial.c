#include "Initial.h"
#include "board.h"

void Initial(void)
{
    scheduler_init(); // 初始化任务调度器
    OLED_Init();      // 初始化OLED显示
    BT_DAMConfig();   // 配置DMA

    OLED_ShowString(0, 0, (const uint8_t *)"Wait"); // 
    OLED_Refresh_Gram();             // 刷新OLED显示
    MPU6050_Init();                  // 初始化MPU6050传感器
    OLED_Clear();                    // 清除OLED显示
    OLED_Refresh_Gram();             // 刷新OLED显示
}