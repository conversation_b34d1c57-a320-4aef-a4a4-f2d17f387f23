#include "key.h"

// 读取按键当前状态
// 返回值：0 表示按键未按下，1 表示按键按下
uint8_t keyValue(void)
{
    return (DL_GPIO_readPins(KEY_PORT, KEY_key_PIN) & KEY_key_PIN) > 0 ? 0 : 1;
}

/**
 * @brief 按键扫描函数，支持单击、双击、长按识别
 * @param freq 扫描频率（Hz），用于计算时间基准
 * @return UserKeyState_t 按键状态（无状态/单击/双击/长按）
 *
 * 主要逻辑：
 * 1. 检测按键是否按下，记录按下时间。
 * 2. 按下超过1秒判定为长按。
 * 3. 按下后弹起，若在50~300ms内再次按下判定为双击。
 * 4. 按下后弹起，超过800ms未再次按下判定为单击。
 * 5. 每次识别后清空标志，等待下一次识别。
 */
UserKeyState_t key_scan(uint16_t freq)
{
    static uint16_t time_core;       // 走时核心
    static uint16_t long_press_time; // 长按识别
    static uint8_t press_flag = 0;   // 按键按下标记
    static uint8_t check_once = 0;   // 是否已经识别1次标记

    float Count_time = (((float)(1.0f / (float)freq)) * 1000.0f); // 算出计1需要多少个毫秒

    if (check_once) // 完成了识别，则清空所有变量
    {
        press_flag = 0;      // 完成了1次识别，标记清零
        time_core = 0;       // 完成了1次识别，时间清零
        long_press_time = 0; // 完成了1次识别，时间清零
    }
    if (check_once && 1 == keyValue())
        check_once = 0; // 完成扫描后按键被弹起，则开启下一次扫描

    if (0 == keyValue() && check_once == 0) // 按键扫描
    {
        press_flag = 1; // 标记被按下1次
        long_press_time++;
    }

    if (long_press_time > (uint16_t)(500.0f / Count_time)) // 长按1秒
    {
        check_once = 1;           // 标记已被识别
        return USEKEY_long_click; // 长按
    }

    // 按键被按下1次又弹起后，开启内核走时
    if (press_flag && 1 == keyValue())
    {
        time_core++;
    }

    if (press_flag && (time_core > (uint16_t)(50.0f / Count_time) && time_core < (uint16_t)(300.0f / Count_time))) // 50~300ms内被再次按下
    {
        if (0 == keyValue()) // 如果再次按下
        {
            check_once = 1;             // 标记已被识别
            return USEKEY_double_click; // 标记为双击
        }
    }
    else if (press_flag && time_core > (uint16_t)(300.0f / Count_time))
    {
        check_once = 1;             // 标记已被识别
        return USEKEY_single_click; // 800ms后还没被按下，则是单击
    }

    return USEKEY_stateless;
}
